{"SVM_Interaction Type": {"model_name": "SVM", "target_type": "Interaction Type", "model_type": "traditional", "training_time": 114.33874416351318, "final_metrics": {"accuracy": 0.752, "f1_score": 0.7388263112501393}, "timestamp": "2025-06-02T03:03:50.265914"}, "SVM_Severity Level": {"model_name": "SVM", "target_type": "Severity Level", "model_type": "traditional", "training_time": 65.97267937660217, "final_metrics": {"accuracy": 0.8723333333333333, "f1_score": 0.8720086217312757}, "timestamp": "2025-06-02T03:05:01.147541"}, "Naive Bayes_Interaction Type": {"model_name": "<PERSON><PERSON>", "target_type": "Interaction Type", "model_type": "traditional", "training_time": 0.05347704887390137, "final_metrics": {"accuracy": 0.35, "f1_score": 0.36836956495509676}, "timestamp": "2025-06-02T03:05:01.346908"}, "Naive Bayes_Severity Level": {"model_name": "<PERSON><PERSON>", "target_type": "Severity Level", "model_type": "traditional", "training_time": 0.042822837829589844, "final_metrics": {"accuracy": 0.496, "f1_score": 0.4621029854459957}, "timestamp": "2025-06-02T03:05:01.410603"}, "Logistic Regression_Interaction Type": {"model_name": "Logistic Regression", "target_type": "Interaction Type", "model_type": "traditional", "training_time": 44.441444396972656, "final_metrics": {"accuracy": 0.7343333333333333, "f1_score": 0.7284121525623938}, "timestamp": "2025-06-02T03:05:45.887568"}, "Logistic Regression_Severity Level": {"model_name": "Logistic Regression", "target_type": "Severity Level", "model_type": "traditional", "training_time": 2.612036943435669, "final_metrics": {"accuracy": 0.823, "f1_score": 0.8209268622332847}, "timestamp": "2025-06-02T03:05:48.531419"}, "XGBoost_Interaction Type": {"model_name": "XGBoost", "target_type": "Interaction Type", "model_type": "ensemble", "training_time": 46.51325702667236, "final_metrics": {"accuracy": 0.9136666666666666, "f1_score": 0.9124718736588521}, "timestamp": "2025-06-02T03:06:35.567447"}, "XGBoost_Severity Level": {"model_name": "XGBoost", "target_type": "Severity Level", "model_type": "ensemble", "training_time": 3.3532967567443848, "final_metrics": {"accuracy": 0.9773333333333334, "f1_score": 0.9772888189032446}, "timestamp": "2025-06-02T03:06:38.991288"}, "LightGBM_Interaction Type": {"model_name": "LightGBM", "target_type": "Interaction Type", "model_type": "ensemble", "training_time": 3.7251062393188477, "final_metrics": {"accuracy": 0.19466666666666665, "f1_score": 0.15409117330633976}, "timestamp": "2025-06-02T03:06:42.960300"}, "LightGBM_Severity Level": {"model_name": "LightGBM", "target_type": "Severity Level", "model_type": "ensemble", "training_time": 1.0027575492858887, "final_metrics": {"accuracy": 0.971, "f1_score": 0.9708582325620813}, "timestamp": "2025-06-02T03:06:44.075034"}, "CNN_Interaction Type": {"model_name": "CNN", "target_type": "Interaction Type", "model_type": "deep_learning", "training_time": 77.00784969329834, "final_metrics": {"accuracy": 0.5306666666666666, "f1_score": 0.49764082300146917}, "timestamp": "2025-06-02T03:08:03.661262", "epochs": 50, "training_loss": [2.499234437942505, 2.2475244998931885, 2.1407065391540527, 2.0949699878692627, 2.058389186859131, 2.0310816764831543, 2.0006790161132812, 1.9841543436050415, 1.9671497344970703, 1.9328163862228394, 1.899692177772522, 1.8733429908752441, 1.8579181432724, 1.8390713930130005, 1.8159799575805664, 1.8055936098098755, 1.7870041131973267, 1.7789244651794434, 1.7628061771392822, 1.7396562099456787, 1.7219069004058838, 1.7055898904800415, 1.67985200881958, 1.6595379114151, 1.6478971242904663, 1.6334308385849, 1.6278722286224365, 1.6098312139511108, 1.5962028503417969, 1.584420084953308, 1.5827443599700928, 1.5728273391723633, 1.569169282913208, 1.5596669912338257, 1.5528757572174072, 1.5436664819717407, 1.5306837558746338, 1.524776577949524, 1.5266051292419434, 1.500519037246704, 1.4995784759521484, 1.5024940967559814, 1.496096134185791, 1.4928914308547974, 1.4866552352905273, 1.4804673194885254, 1.4777374267578125, 1.4715702533721924, 1.4570366144180298, 1.460413932800293], "validation_loss": [2.288848400115967, 2.1585512161254883, 2.0485427379608154, 1.9953489303588867, 1.9619332551956177, 1.9259909391403198, 1.9012436866760254, 1.874670386314392, 1.8615498542785645, 1.807375431060791, 1.776975393295288, 1.7491189241409302, 1.7273942232131958, 1.7132205963134766, 1.6910213232040405, 1.66943359375, 1.6547504663467407, 1.6394189596176147, 1.6173207759857178, 1.6063231229782104, 1.5690423250198364, 1.5427316427230835, 1.5198235511779785, 1.5124338865280151, 1.5021896362304688, 1.4838519096374512, 1.4709779024124146, 1.4664459228515625, 1.4472572803497314, 1.438927412033081, 1.448918104171753, 1.422784447669983, 1.4191128015518188, 1.4114575386047363, 1.4028501510620117, 1.3966108560562134, 1.385312557220459, 1.3918275833129883, 1.3716260194778442, 1.358573317527771, 1.3627033233642578, 1.3602086305618286, 1.3447620868682861, 1.3477590084075928, 1.3342865705490112, 1.338796615600586, 1.321092128753662, 1.307640552520752, 1.3107107877731323, 1.317496657371521], "training_accuracy": [0.2291666716337204, 0.25733333826065063, 0.28816667199134827, 0.296999990940094, 0.3057500123977661, 0.30566665530204773, 0.3136666715145111, 0.3267500102519989, 0.32858332991600037, 0.3375833332538605, 0.3545833230018616, 0.36375001072883606, 0.3648333251476288, 0.3696666657924652, 0.3789166808128357, 0.38366666436195374, 0.3865833282470703, 0.3865000009536743, 0.39500001072883606, 0.4026666581630707, 0.40558332204818726, 0.41216665506362915, 0.4179166555404663, 0.42091667652130127, 0.4313333332538605, 0.4414166808128357, 0.4455833435058594, 0.4440000057220459, 0.44858333468437195, 0.44858333468437195, 0.45225000381469727, 0.4569999873638153, 0.4529166519641876, 0.45883333683013916, 0.460999995470047, 0.4634999930858612, 0.46549999713897705, 0.46799999475479126, 0.46191665530204773, 0.47583332657814026, 0.4741666615009308, 0.47441667318344116, 0.4753333330154419, 0.4777500033378601, 0.48100000619888306, 0.48091667890548706, 0.48391667008399963, 0.48133334517478943, 0.4908333420753479, 0.48875001072883606], "validation_accuracy": [0.25566667318344116, 0.2939999997615814, 0.29366666078567505, 0.30566665530204773, 0.3166666626930237, 0.3153333365917206, 0.328000009059906, 0.3453333377838135, 0.3529999852180481, 0.36666667461395264, 0.3840000033378601, 0.3903333246707916, 0.3973333239555359, 0.39399999380111694, 0.4086666703224182, 0.4090000092983246, 0.4103333353996277, 0.4176666736602783, 0.4243333339691162, 0.43700000643730164, 0.4436666667461395, 0.4493333399295807, 0.453000009059906, 0.4636666774749756, 0.468666672706604, 0.4676666557788849, 0.47433334589004517, 0.4729999899864197, 0.47966668009757996, 0.47833332419395447, 0.484333336353302, 0.4819999933242798, 0.4883333444595337, 0.4833333194255829, 0.4816666543483734, 0.49133333563804626, 0.49566665291786194, 0.49666666984558105, 0.4973333477973938, 0.5073333382606506, 0.5099999904632568, 0.5120000243186951, 0.515666663646698, 0.5103333592414856, 0.515333354473114, 0.5233333110809326, 0.527999997138977, 0.5306666493415833, 0.5296666622161865, 0.5316666960716248], "best_epoch": 48.0, "best_val_loss": 1.307640552520752, "best_val_accuracy": 0.5316666960716248}, "CNN_Severity Level": {"model_name": "CNN", "target_type": "Severity Level", "model_type": "deep_learning", "training_time": 74.98458623886108, "final_metrics": {"accuracy": 0.7606666666666667, "f1_score": 0.7571447996970947}, "timestamp": "2025-06-02T03:09:19.573760", "epochs": 50, "training_loss": [0.9427193999290466, 0.8725412487983704, 0.8519030809402466, 0.8406795859336853, 0.826378345489502, 0.8060336112976074, 0.7833969593048096, 0.7792540788650513, 0.7658738493919373, 0.7557952404022217, 0.7528115510940552, 0.7476029992103577, 0.735877513885498, 0.7369906306266785, 0.72332364320755, 0.7203705310821533, 0.717520534992218, 0.7085959315299988, 0.7066131234169006, 0.7000448107719421, 0.691519021987915, 0.6955463290214539, 0.6847776770591736, 0.6811893582344055, 0.6799754500389099, 0.6767114400863647, 0.6680687069892883, 0.6709179878234863, 0.6644847989082336, 0.6624227166175842, 0.6584867238998413, 0.6520214080810547, 0.6583797931671143, 0.6564805507659912, 0.6471572518348694, 0.6411354541778564, 0.6438001394271851, 0.6376993060112, 0.6373012065887451, 0.6343231201171875, 0.6339434385299683, 0.6298218369483948, 0.6261942386627197, 0.629558265209198, 0.6288598775863647, 0.6228799223899841, 0.6140584945678711, 0.6191983819007874, 0.6129614114761353, 0.6109310984611511], "validation_loss": [0.8687133193016052, 0.8333099484443665, 0.821614682674408, 0.8129992485046387, 0.7910175919532776, 0.7577852606773376, 0.7533034086227417, 0.7373961806297302, 0.7248379588127136, 0.7169954180717468, 0.7075785398483276, 0.7000542879104614, 0.6926460862159729, 0.6823781132698059, 0.6711169481277466, 0.6709417104721069, 0.6547912955284119, 0.6526067852973938, 0.6447362303733826, 0.6397554874420166, 0.6385509967803955, 0.6230484843254089, 0.6262484788894653, 0.6160622835159302, 0.6169961094856262, 0.6186837553977966, 0.6152264475822449, 0.6126567125320435, 0.6099708080291748, 0.5975291132926941, 0.6067275404930115, 0.5942168831825256, 0.5927661657333374, 0.5889299511909485, 0.5802549123764038, 0.5904581546783447, 0.5826404690742493, 0.578660249710083, 0.5729790329933167, 0.5733016133308411, 0.5647276043891907, 0.5593560934066772, 0.5627274513244629, 0.5545086860656738, 0.5562881827354431, 0.55087810754776, 0.5444478988647461, 0.5514758825302124, 0.5394093990325928, 0.5377485156059265], "training_accuracy": [0.5733333230018616, 0.5786666870117188, 0.5816666483879089, 0.5809999704360962, 0.593833327293396, 0.6044166684150696, 0.628250002861023, 0.628166675567627, 0.637583315372467, 0.6460833549499512, 0.6489166617393494, 0.6524166464805603, 0.6553333401679993, 0.6541666388511658, 0.6626666784286499, 0.6630833148956299, 0.6708333492279053, 0.6701666712760925, 0.672166645526886, 0.6778333187103271, 0.6781666874885559, 0.6802499890327454, 0.6869166493415833, 0.684166669845581, 0.6913333535194397, 0.6936666369438171, 0.6913333535194397, 0.6910833120346069, 0.6954166889190674, 0.6966666579246521, 0.706250011920929, 0.7017499804496765, 0.6990833282470703, 0.7070000171661377, 0.7093333601951599, 0.715666651725769, 0.7116666436195374, 0.7116666436195374, 0.7164999842643738, 0.7160000205039978, 0.7132499814033508, 0.715416669845581, 0.7199166417121887, 0.7214166522026062, 0.7136666774749756, 0.7208333611488342, 0.7258333563804626, 0.7206666469573975, 0.7286666631698608, 0.7273333072662354], "validation_accuracy": [0.5849999785423279, 0.5849999785423279, 0.5820000171661377, 0.5836666822433472, 0.6256666779518127, 0.6233333349227905, 0.6286666393280029, 0.652999997138977, 0.6636666655540466, 0.6610000133514404, 0.6639999747276306, 0.6743333339691162, 0.6833333373069763, 0.6806666851043701, 0.6933333277702332, 0.6809999942779541, 0.7023333311080933, 0.7026666402816772, 0.7093333601951599, 0.7076666951179504, 0.703000009059906, 0.7110000252723694, 0.7076666951179504, 0.7073333263397217, 0.7103333473205566, 0.7160000205039978, 0.7126666903495789, 0.7153333425521851, 0.718999981880188, 0.7233333587646484, 0.7213333249092102, 0.7306666374206543, 0.7286666631698608, 0.7310000061988831, 0.7369999885559082, 0.7296666502952576, 0.7360000014305115, 0.7476666569709778, 0.7453333139419556, 0.7483333349227905, 0.753333330154419, 0.753000020980835, 0.7519999742507935, 0.7580000162124634, 0.7509999871253967, 0.7583333253860474, 0.7646666765213013, 0.7680000066757202, 0.768666684627533, 0.7606666684150696], "best_epoch": 50.0, "best_val_loss": 0.5377485156059265, "best_val_accuracy": 0.768666684627533}, "LSTM_Interaction Type": {"model_name": "LSTM", "target_type": "Interaction Type", "model_type": "deep_learning", "training_time": 8409.155714988708, "final_metrics": {"accuracy": 0.25566666666666665, "f1_score": 0.10411273338642597}, "timestamp": "2025-06-02T05:29:58.311936", "epochs": 24, "training_loss": [2.4496026039123535, 2.2799582481384277, 2.2551686763763428, 2.2457094192504883, 2.2346458435058594, 2.2313692569732666, 2.2298243045806885, 2.2224395275115967, 2.218073606491089, 2.2261409759521484, 2.2366538047790527, 2.233630418777466, 2.233515739440918, 2.234055757522583, 2.2320973873138428, 2.230264186859131, 2.226832151412964, 2.2304599285125732, 2.2284886837005615, 2.230135440826416, 2.226222276687622, 2.2286057472229004, 2.228433847427368, 2.228452682495117], "validation_loss": [2.2032690048217773, 2.2018399238586426, 2.1988112926483154, 2.1948368549346924, 2.1873767375946045, 2.1855661869049072, 2.186434507369995, 2.1732735633850098, 2.1715664863586426, 2.2009127140045166, 2.200955629348755, 2.200899839401245, 2.2006638050079346, 2.2003068923950195, 2.1995606422424316, 2.1991660594940186, 2.199475049972534, 2.1995248794555664, 2.1991939544677734, 2.197855234146118, 2.197753667831421, 2.1979808807373047, 2.197930335998535, 2.197814702987671], "training_accuracy": [0.22308333218097687, 0.25049999356269836, 0.26258334517478943, 0.2667500078678131, 0.2680000066757202, 0.2681666612625122, 0.26866665482521057, 0.2685000002384186, 0.2682499885559082, 0.2685000002384186, 0.2685000002384186, 0.2685000002384186, 0.2685000002384186, 0.2685000002384186, 0.2685000002384186, 0.2685000002384186, 0.2685000002384186, 0.2685000002384186, 0.2685000002384186, 0.2685000002384186, 0.2685000002384186, 0.2685000002384186, 0.2685000002384186, 0.2685000002384186], "validation_accuracy": [0.25566667318344116, 0.25566667318344116, 0.25566667318344116, 0.25566667318344116, 0.25566667318344116, 0.25566667318344116, 0.25566667318344116, 0.25566667318344116, 0.25566667318344116, 0.25566667318344116, 0.25566667318344116, 0.25566667318344116, 0.25566667318344116, 0.25566667318344116, 0.25566667318344116, 0.25566667318344116, 0.25566667318344116, 0.25566667318344116, 0.25566667318344116, 0.25566667318344116, 0.25566667318344116, 0.25566667318344116, 0.25566667318344116, 0.25566667318344116], "best_epoch": 9.0, "best_val_loss": 2.1715664863586426, "best_val_accuracy": 0.25566667318344116}, "LSTM_Severity Level": {"model_name": "LSTM", "target_type": "Severity Level", "model_type": "deep_learning", "training_time": 13592.02590560913, "final_metrics": {"accuracy": 0.5766666666666667, "f1_score": 0.4218322762508809}, "timestamp": "2025-06-02T09:17:00.741647", "epochs": 39, "training_loss": [0.9552617073059082, 0.9514322280883789, 0.9481439590454102, 0.945841372013092, 0.9444935917854309, 0.9442002773284912, 0.9436289072036743, 0.9434672594070435, 0.9430261850357056, 0.9437685608863831, 0.9432869553565979, 0.943215012550354, 0.9439211487770081, 0.942947268486023, 0.9426987171173096, 0.9428818225860596, 0.9431750774383545, 0.942588210105896, 0.9426698684692383, 0.9431641697883606, 0.9434717893600464, 0.9426845908164978, 0.9425628185272217, 0.9430530071258545, 0.9426552057266235, 0.9427180886268616, 0.941969096660614, 0.9424368739128113, 0.9424484968185425, 0.9430457949638367, 0.9431219696998596, 0.9427111744880676, 0.9428067207336426, 0.942274808883667, 0.9419934749603271, 0.9421674609184265, 0.9420271515846252, 0.9423692226409912, 0.9423132538795471], "validation_loss": [0.9428570866584778, 0.9373940229415894, 0.9355977177619934, 0.9353839755058289, 0.9354747533798218, 0.9354398846626282, 0.9354405999183655, 0.9353933930397034, 0.9353837966918945, 0.9354124069213867, 0.9354782104492188, 0.935416042804718, 0.935427188873291, 0.9354281425476074, 0.9352964162826538, 0.9353324174880981, 0.9353309869766235, 0.9353127479553223, 0.9353129267692566, 0.9353248476982117, 0.9353215098381042, 0.9352988004684448, 0.9353230595588684, 0.9352942109107971, 0.9353224039077759, 0.9353176951408386, 0.9353394508361816, 0.9353265166282654, 0.9353176951408386, 0.9353439211845398, 0.93534255027771, 0.9353387951850891, 0.9353434443473816, 0.9353185892105103, 0.9353395104408264, 0.9353432059288025, 0.9353501200675964, 0.9353737235069275, 0.9353700280189514], "training_accuracy": [0.5736666917800903, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496, 0.5735833048820496], "validation_accuracy": [0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805, 0.5766666531562805], "best_epoch": 24.0, "best_val_loss": 0.9352942109107971, "best_val_accuracy": 0.5766666531562805}, "BERT-Inspired_Interaction Type": {"model_name": "BERT-Inspired", "target_type": "Interaction Type", "model_type": "deep_learning", "training_time": 35.10207939147949, "final_metrics": {"accuracy": 0.7793333333333333, "f1_score": 0.7714426660997401}, "timestamp": "2025-06-02T09:17:36.868377", "epochs": 22, "training_loss": [1.5973907709121704, 0.9246598482131958, 0.7805003523826599, 0.6942432522773743, 0.6359321475028992, 0.5961522459983826, 0.5681859254837036, 0.5379537343978882, 0.5192404985427856, 0.4950427711009979, 0.4781664311885834, 0.4674641489982605, 0.44844335317611694, 0.4345169961452484, 0.42354756593704224, 0.40652909874916077, 0.410635769367218, 0.3594047725200653, 0.3458642065525055, 0.3371414244174957, 0.3227730095386505, 0.3210626244544983], "validation_loss": [0.9222841262817383, 0.7780336737632751, 0.729887843132019, 0.710129976272583, 0.6869582533836365, 0.674833357334137, 0.6707613468170166, 0.6733755469322205, 0.6795892715454102, 0.6760991811752319, 0.6750094890594482, 0.6795791983604431, 0.6988941431045532, 0.706903338432312, 0.7061172723770142, 0.7225911617279053, 0.7103875279426575, 0.7307685613632202, 0.748559832572937, 0.7571921348571777, 0.7933509349822998, 0.7862154841423035], "training_accuracy": [0.5316666960716248, 0.6958333253860474, 0.7297499775886536, 0.7552499771118164, 0.765916645526886, 0.7770000100135803, 0.7905833125114441, 0.7940000295639038, 0.8019166588783264, 0.8081666827201843, 0.8117499947547913, 0.8189166784286499, 0.8234166502952576, 0.8264166712760925, 0.8299999833106995, 0.8337500095367432, 0.8314999938011169, 0.8513333201408386, 0.8573333621025085, 0.85958331823349, 0.8637499809265137, 0.8632500171661377], "validation_accuracy": [0.6936666369438171, 0.7319999933242798, 0.7423333525657654, 0.746999979019165, 0.7710000276565552, 0.7726666927337646, 0.7793333530426025, 0.7703333497047424, 0.7816666960716248, 0.7896666526794434, 0.7816666960716248, 0.793666660785675, 0.7876666784286499, 0.7916666865348816, 0.7836666703224182, 0.7943333387374878, 0.7929999828338623, 0.7953333258628845, 0.7940000295639038, 0.7993333339691162, 0.7976666688919067, 0.7993333339691162], "best_epoch": 7.0, "best_val_loss": 0.6707613468170166, "best_val_accuracy": 0.7993333339691162}, "BERT-Inspired_Severity Level": {"model_name": "BERT-Inspired", "target_type": "Severity Level", "model_type": "deep_learning", "training_time": 35.9361526966095, "final_metrics": {"accuracy": 0.909, "f1_score": 0.9093081695412569}, "timestamp": "2025-06-02T09:18:13.743106", "epochs": 23, "training_loss": [0.5910236239433289, 0.3408312201499939, 0.2729078233242035, 0.22941434383392334, 0.20451302826404572, 0.1853175312280655, 0.17237509787082672, 0.15980416536331177, 0.15137700736522675, 0.13400797545909882, 0.12963615357875824, 0.1210995465517044, 0.11963178217411041, 0.10954394191503525, 0.1099029928445816, 0.10076909512281418, 0.09983363002538681, 0.10002804547548294, 0.08155865222215652, 0.07150676101446152, 0.07319682091474533, 0.06600522249937057, 0.06706930696964264], "validation_loss": [0.3777755796909332, 0.31360283493995667, 0.2889849543571472, 0.26175644993782043, 0.2477027177810669, 0.2478344589471817, 0.2543790638446808, 0.23872457444667816, 0.24976928532123566, 0.2502839267253876, 0.26562830805778503, 0.26212450861930847, 0.24770739674568176, 0.2524976432323456, 0.2597091794013977, 0.275039404630661, 0.28455570340156555, 0.2886023223400116, 0.2879030406475067, 0.29401811957359314, 0.2900579571723938, 0.3032452166080475, 0.31525593996047974], "training_accuracy": [0.765250027179718, 0.8667500019073486, 0.890916645526886, 0.906583309173584, 0.9175000190734863, 0.9231666922569275, 0.9277499914169312, 0.9335833191871643, 0.9368333220481873, 0.9420833587646484, 0.9429166913032532, 0.9479166865348816, 0.9466666579246521, 0.950166642665863, 0.9517499804496765, 0.9553333520889282, 0.9556666612625122, 0.956083357334137, 0.9631666541099548, 0.965416669845581, 0.9674166440963745, 0.968416690826416, 0.9677500128746033], "validation_accuracy": [0.8360000252723694, 0.8686666488647461, 0.8736666440963745, 0.8899999856948853, 0.8970000147819519, 0.8983333110809326, 0.9013333320617676, 0.9089999794960022, 0.9056666493415833, 0.909333348274231, 0.9113333225250244, 0.9126666784286499, 0.9206666946411133, 0.9236666560173035, 0.925000011920929, 0.9193333387374878, 0.9206666946411133, 0.9203333258628845, 0.9236666560173035, 0.9276666641235352, 0.9286666512489319, 0.9283333420753479, 0.9286666512489319], "best_epoch": 8.0, "best_val_loss": 0.23872457444667816, "best_val_accuracy": 0.9286666512489319}}