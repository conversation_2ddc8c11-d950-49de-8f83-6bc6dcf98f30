--------------------------------------------------
Step 1: Data Loading and Exploration
--------------------------------------------------
Dataset shape: (15000, 18)

First few rows:
  DDInterID_A           Drug_A       Pharmacodynamic_Class_A  LogP_A  \
0  DDInter429          Codeine              Opioid Analgesic    1.45   
1  DDInter569  Diphenhydramine                 Antihistamine    3.45   
2  DDInter776       Formoterol                Beta-2 Agonist    2.12   
3    DDInter8      Abiraterone  Androgen Synthesis Inhibitor    5.12   
4    DDInter8      Abiraterone  Androgen Synthesis Inhibitor    5.12   

  Therapeutic_Index_A               Transporter_Interaction_A  \
0             Non-NTI                         Substrate: P-gp   
1             Non-NTI                         Substrate: P-gp   
2             Non-NTI                          No Transporter   
3             Non-NTI  Substrate: P-gp / Inhibitor: P-gp;BCRP   
4             Non-NTI  Substrate: P-gp / Inhibitor: P-gp;BCRP   

  Plasma_Protein_Binding_A                   Metabolic_Pathways_A  \
0                      25%               Substrate: CYP2D6;CYP3A4   
1                      99%                      Substrate: CYP2D6   
2                      64%                      Substrate: UGT1A9   
3                      99%  Substrate: CYP3A4 / Inhibitor: CYP2D6   
4                      99%  Substrate: CYP3A4 / Inhibitor: CYP2D6   

   DDInterID_B       Drug_B  \
0     DDInter8  Abiraterone   
1     DDInter8  Abiraterone   
2     DDInter8  Abiraterone   
3   DDInter997    Ivacaftor   
4  DDInter1331   Olodaterol   

                             Pharmacodynamic_Class_B  LogP_B  \
0                       Androgen Synthesis Inhibitor    5.12   
1                       Androgen Synthesis Inhibitor    5.12   
2                       Androgen Synthesis Inhibitor    5.12   
3  Cystic Fibrosis Transmembrane Conductance Regu...    5.67   
4                                     Beta-2 Agonist    2.96   

  Therapeutic_Index_B               Transporter_Interaction_B  \
0             Non-NTI  Substrate: P-gp / Inhibitor: P-gp;BCRP   
1             Non-NTI  Substrate: P-gp / Inhibitor: P-gp;BCRP   
2             Non-NTI  Substrate: P-gp / Inhibitor: P-gp;BCRP   
3             Non-NTI  Substrate: P-gp;BCRP / Inhibitor: P-gp   
4             Non-NTI                          No Transporter   

  Plasma_Protein_Binding_B                   Metabolic_Pathways_B  \
0                      99%  Substrate: CYP3A4 / Inhibitor: CYP2D6   
1                      99%  Substrate: CYP3A4 / Inhibitor: CYP2D6   
2                      99%  Substrate: CYP3A4 / Inhibitor: CYP2D6   
3                      99%                      Substrate: CYP3A4   
4                      60%               Substrate: UGT1A1;UGT2B7   

      interaction_type     Level  
0  serum concentration  Moderate  
1  serum concentration  Moderate  
2  serum concentration  Moderate  
3  serum concentration  Moderate  
4  serum concentration  Moderate  

Missing values per column:
DDInterID_A                  0
Drug_A                       0
Pharmacodynamic_Class_A      0
LogP_A                       0
Therapeutic_Index_A          0
Transporter_Interaction_A    0
Plasma_Protein_Binding_A     0
Metabolic_Pathways_A         0
DDInterID_B                  0
Drug_B                       0
Pharmacodynamic_Class_B      0
LogP_B                       0
Therapeutic_Index_B          0
Transporter_Interaction_B    0
Plasma_Protein_Binding_B     0
Metabolic_Pathways_B         0
interaction_type             0
Level                        0
dtype: int64

Data types:
DDInterID_A                   object
Drug_A                        object
Pharmacodynamic_Class_A       object
LogP_A                       float64
Therapeutic_Index_A           object
Transporter_Interaction_A     object
Plasma_Protein_Binding_A      object
Metabolic_Pathways_A          object
DDInterID_B                   object
Drug_B                        object
Pharmacodynamic_Class_B       object
LogP_B                       float64
Therapeutic_Index_B           object
Transporter_Interaction_B     object
Plasma_Protein_Binding_B      object
Metabolic_Pathways_B          object
interaction_type              object
Level                         object
dtype: object

Basic statistics for numerical columns:
             LogP_A        LogP_B
count  15000.000000  15000.000000
mean       2.915836      3.196885
std        1.650312      1.650884
min       -4.120000     -4.230000
25%        1.910000      2.150000
50%        2.890000      3.160000
75%        3.920000      4.560000
max       10.230000     10.230000

Step 2: Data Preprocessing
--------------------------------------------------
Preprocessing the dataset...

Step 3: Feature Engineering
--------------------------------------------------
Total unique enzymes found: 25
Total unique transporters found: 8
Dataset shape after feature engineering: (15000, 309)

Step 4: Preparing Data for Modeling
--------------------------------------------------
Number of features: 293
Interaction types: ['QTc-prolonging activities' 'absorption' 'anticholinergic activities'
 'anticoagulant activities' 'antihypertensive activities'
 'antiplatelet activities' 'arrhythmogenic activities'
 'atrioventricular blocking (AV block) activities' 'bioavailability'
 'bradycardic activities' 'bronchodilatory activities'
 'cardiotoxic activities'
 'central nervous system depressant (CNS depressant) activities'
 'dermatologic adverse activities' 'excretion' 'excretion rate'
 'excretion rate of higher serum level' 'hyperglycemic activities'
 'hypertensive activities' 'hypoglycemic activities'
 'hypokalemic activities' 'hypotensive activities'
 'immunosuppressive activities' 'metabolism' 'myelosuppressive activities'
 'nephrotoxic activities' 'neuroexcitatory activities'
 'neurotoxic activities' 'orthostatic hypotensive activities'
 'risk of hypersensitivity reaction to'
 'risk or severity of QTc prolongation'
 'risk or severity of adverse effects' 'risk or severity of bradycardia'
 'risk or severity of hypotension'
 'risk or severity of ventricular arrhythmias' 'sedative activities'
 'serotonergic activities' 'serum concentration'
 'serum concentration of active metabolites' 'stimulatory activities'
 'tachycardic activities' 'therapeutic efficacy'
 'vasoconstricting activities' 'vasodilatory activities']
Severity levels: ['Major' 'Minor' 'Moderate']
Training set size: (12000, 293)
Testing set size: (3000, 293)

Interaction Type Distribution:
37    3222
23    2205
0     1673
31    1383
41    1332
12     384
20     359
30     279
19     223
22     198
8       97
1       90
21      89
3       81
4       78
42      44
35      27
28      23
25      23
9       22
36      22
38      21
26      15
6       13
39      12
5       12
11      11
10       8
40       8
18       7
17       7
33       6
2        5
14       4
13       4
7        3
43       2
32       2
24       1
16       1
27       1
15       1
34       1
29       1
Name: count, dtype: int64

Severity Level Distribution:
2    6883
0    3595
1    1522
Name: count, dtype: int64

Data preparation complete. Ready for model implementation.

Step 5: Model Implementation and Training
--------------------------------------------------

5.1 Support Vector Machine (SVM)
------------------------------
Current SVM parameters:
  C: 1.0
  kernel: rbf
  degree: 3
  gamma: scale
  coef0: 0.0
  shrinking: True
  probability: True
  tol: 0.001
  cache_size: 200
  class_weight: None
  verbose: False
  max_iter: -1
  decision_function_shape: ovr
  break_ties: False
  random_state: 42

Training SVM for Interaction Type...
Training completed in 114.34 seconds
SVM - Interaction Type Accuracy: 0.7520
SVM - Interaction Type F1 Score: 0.7388

Classification Report for SVM - Interaction Type:
              precision    recall  f1-score   support

           0       0.66      0.81      0.73       442
           1       1.00      0.75      0.86        20
           2       0.00      0.00      0.00         1
           3       0.78      0.82      0.80        22
           4       0.75      0.50      0.60        18
           5       1.00      0.25      0.40         4
           6       0.00      0.00      0.00         2
           7       0.00      0.00      0.00         1
           8       1.00      1.00      1.00        21
           9       1.00      0.33      0.50         3
          10       0.00      0.00      0.00         2
          11       1.00      0.50      0.67         4
          12       0.79      0.67      0.73        93
          13       0.00      0.00      0.00         1
          19       0.98      0.81      0.89        58
          20       1.00      0.99      1.00       109
          21       0.67      0.62      0.65        16
          22       0.80      0.95      0.87        38
          23       0.73      0.57      0.64       581
          25       1.00      0.75      0.86         4
          26       1.00      0.50      0.67         2
          28       1.00      0.40      0.57        10
          30       1.00      0.03      0.06        68
          31       0.76      0.89      0.82       359
          33       0.00      0.00      0.00         1
          35       0.62      0.62      0.62         8
          36       0.00      0.00      0.00         4
          37       0.69      0.80      0.74       767
          38       0.00      0.00      0.00         3
          39       0.33      1.00      0.50         1
          40       0.00      0.00      0.00         1
          41       0.93      0.89      0.91       322
          42       1.00      0.14      0.25        14

    accuracy                           0.75      3000
   macro avg       0.62      0.47      0.49      3000
weighted avg       0.76      0.75      0.74      3000

Model summary saved to model_summary_svm_interaction_type_20250602_030350.txt
Model saved to saved_models/svm_interaction_type_20250602_030350.pkl

Training SVM for Severity Level...
Training completed in 65.97 seconds
SVM - Severity Level Accuracy: 0.8723
SVM - Severity Level F1 Score: 0.8720

Classification Report for SVM - Severity Level:
              precision    recall  f1-score   support

           0       0.82      0.81      0.82       905
           1       0.97      0.81      0.88       365
           2       0.88      0.92      0.90      1730

    accuracy                           0.87      3000
   macro avg       0.89      0.85      0.87      3000
weighted avg       0.87      0.87      0.87      3000

Model summary saved to model_summary_svm_severity_level_20250602_030501.txt
Model saved to saved_models/svm_severity_level_20250602_030501.pkl

5.2 Naive Bayes
------------------------------
Current Naive Bayes parameters:
  priors: None
  var_smoothing: 1e-09

Training Naive Bayes for Interaction Type...
Training completed in 0.05 seconds
Naive Bayes - Interaction Type Accuracy: 0.3500
Naive Bayes - Interaction Type F1 Score: 0.3684

Classification Report for Naive Bayes - Interaction Type:
              precision    recall  f1-score   support

           0       0.52      0.32      0.40       442
           1       0.14      0.75      0.23        20
           2       0.00      0.00      0.00         1
           3       0.06      0.82      0.11        22
           4       0.82      0.50      0.62        18
           5       0.40      1.00      0.57         4
           6       0.10      0.50      0.17         2
           7       0.00      0.00      0.00         1
           8       1.00      1.00      1.00        21
           9       0.03      0.67      0.06         3
          10       1.00      1.00      1.00         2
          11       0.50      0.50      0.50         4
          12       0.43      0.67      0.52        93
          13       1.00      1.00      1.00         1
          14       0.00      0.00      0.00         0
          17       0.00      0.00      0.00         0
          19       0.36      0.84      0.51        58
          20       1.00      1.00      1.00       109
          21       0.14      0.81      0.24        16
          22       0.24      1.00      0.39        38
          23       0.61      0.20      0.30       581
          25       0.50      1.00      0.67         4
          26       0.07      1.00      0.13         2
          28       0.40      0.60      0.48        10
          30       0.10      0.41      0.17        68
          31       0.30      0.12      0.17       359
          33       0.33      1.00      0.50         1
          35       0.10      0.88      0.18         8
          36       0.11      1.00      0.19         4
          37       0.92      0.18      0.30       767
          38       0.01      0.67      0.02         3
          39       0.50      1.00      0.67         1
          40       1.00      1.00      1.00         1
          41       0.52      0.60      0.56       322
          42       0.21      1.00      0.35        14

    accuracy                           0.35      3000
   macro avg       0.38      0.66      0.40      3000
weighted avg       0.60      0.35      0.37      3000

Model summary saved to model_summary_naive_bayes_interaction_type_20250602_030501.txt
Model saved to saved_models/naive_bayes_interaction_type_20250602_030501.pkl

Training Naive Bayes for Severity Level...
Training completed in 0.04 seconds
Naive Bayes - Severity Level Accuracy: 0.4960
Naive Bayes - Severity Level F1 Score: 0.4621

Classification Report for Naive Bayes - Severity Level:
              precision    recall  f1-score   support

           0       0.38      0.99      0.55       905
           1       0.95      0.47      0.63       365
           2       0.89      0.24      0.38      1730

    accuracy                           0.50      3000
   macro avg       0.74      0.57      0.52      3000
weighted avg       0.74      0.50      0.46      3000

Model summary saved to model_summary_naive_bayes_severity_level_20250602_030501.txt
Model saved to saved_models/naive_bayes_severity_level_20250602_030501.pkl

5.3 Logistic Regression
------------------------------
Current Logistic Regression parameters:
  penalty: l2
  dual: False
  tol: 0.0001
  C: 1.0
  fit_intercept: True
  intercept_scaling: 1
  class_weight: None
  random_state: 42
  solver: lbfgs
  max_iter: 1000
  multi_class: multinomial
  verbose: 0
  warm_start: False
  n_jobs: None
  l1_ratio: None

Training Logistic Regression for Interaction Type...
Training completed in 44.44 seconds
Logistic Regression - Interaction Type Accuracy: 0.7343
Logistic Regression - Interaction Type F1 Score: 0.7284

Classification Report for Logistic Regression - Interaction Type:
              precision    recall  f1-score   support

           0       0.67      0.77      0.72       442
           1       0.84      0.80      0.82        20
           2       0.00      0.00      0.00         1
           3       0.86      0.86      0.86        22
           4       0.93      0.72      0.81        18
           5       0.75      0.75      0.75         4
           6       0.00      0.00      0.00         2
           7       0.00      0.00      0.00         1
           8       1.00      1.00      1.00        21
           9       0.50      0.33      0.40         3
          10       1.00      1.00      1.00         2
          11       1.00      1.00      1.00         4
          12       0.85      0.69      0.76        93
          13       1.00      1.00      1.00         1
          19       0.96      0.93      0.95        58
          20       1.00      1.00      1.00       109
          21       0.60      0.75      0.67        16
          22       0.81      1.00      0.89        38
          23       0.68      0.56      0.62       581
          25       0.80      1.00      0.89         4
          26       1.00      0.50      0.67         2
          28       0.80      0.40      0.53        10
          29       0.00      0.00      0.00         0
          30       0.39      0.18      0.24        68
          31       0.68      0.81      0.74       359
          33       0.00      0.00      0.00         1
          35       0.78      0.88      0.82         8
          36       0.50      0.50      0.50         4
          37       0.70      0.72      0.71       767
          38       0.00      0.00      0.00         3
          39       0.00      0.00      0.00         1
          40       0.00      0.00      0.00         1
          41       0.90      0.90      0.90       322
          42       0.60      0.86      0.71        14

    accuracy                           0.73      3000
   macro avg       0.61      0.59      0.59      3000
weighted avg       0.73      0.73      0.73      3000

Model summary saved to model_summary_logistic_regression_interaction_type_20250602_030545.txt
Model saved to saved_models/logistic_regression_interaction_type_20250602_030545.pkl

Training Logistic Regression for Severity Level...
Training completed in 2.61 seconds
Logistic Regression - Severity Level Accuracy: 0.8230
Logistic Regression - Severity Level F1 Score: 0.8209

Classification Report for Logistic Regression - Severity Level:
              precision    recall  f1-score   support

           0       0.77      0.72      0.74       905
           1       0.84      0.73      0.78       365
           2       0.84      0.90      0.87      1730

    accuracy                           0.82      3000
   macro avg       0.82      0.78      0.80      3000
weighted avg       0.82      0.82      0.82      3000

Model summary saved to model_summary_logistic_regression_severity_level_20250602_030548.txt
Model saved to saved_models/logistic_regression_severity_level_20250602_030548.pkl

5.4 XGBoost
------------------------------
Current XGBoost parameters:
  max_depth: 6
  learning_rate: 0.3
  n_estimators: 400
  verbosity: 0
  objective: multi:softprob
  booster: gbtree
  tree_method: auto
  n_jobs: -1
  gamma: 0
  min_child_weight: 1
  max_delta_step: 0
  subsample: 1
  colsample_bytree: 1
  colsample_bylevel: 1
  colsample_bynode: 1
  reg_alpha: 0
  reg_lambda: 1
  scale_pos_weight: 1
  base_score: 0.5
  random_state: 42
  num_parallel_tree: 1
  importance_type: gain
  gpu_id: -1
  validate_parameters: True
Cleaned XGBoost parameters (removed None values):
  max_depth: 6
  learning_rate: 0.3
  n_estimators: 400
  verbosity: 0
  objective: multi:softprob
  booster: gbtree
  tree_method: auto
  n_jobs: -1
  gamma: 0
  min_child_weight: 1
  max_delta_step: 0
  subsample: 1
  colsample_bytree: 1
  colsample_bylevel: 1
  colsample_bynode: 1
  reg_alpha: 0
  reg_lambda: 1
  scale_pos_weight: 1
  base_score: 0.5
  random_state: 42
  num_parallel_tree: 1
  importance_type: gain
  gpu_id: -1
  validate_parameters: True

Training XGBoost for Interaction Type...
Training completed in 46.51 seconds
XGBoost - Interaction Type Accuracy: 0.9137
XGBoost - Interaction Type F1 Score: 0.9125

Classification Report for XGBoost - Interaction Type:
              precision    recall  f1-score   support

           0       0.88      0.91      0.90       442
           1       1.00      0.85      0.92        20
           2       0.00      0.00      0.00         1
           3       0.90      0.86      0.88        22
           4       1.00      0.67      0.80        18
           5       1.00      0.75      0.86         4
           6       1.00      0.50      0.67         2
           7       0.00      0.00      0.00         1
           8       1.00      1.00      1.00        21
           9       1.00      0.33      0.50         3
          10       1.00      1.00      1.00         2
          11       1.00      1.00      1.00         4
          12       0.95      0.95      0.95        93
          13       1.00      1.00      1.00         1
          19       1.00      0.93      0.96        58
          20       1.00      1.00      1.00       109
          21       0.93      0.81      0.87        16
          22       0.97      1.00      0.99        38
          23       0.88      0.88      0.88       581
          25       0.80      1.00      0.89         4
          26       1.00      0.50      0.67         2
          28       1.00      0.60      0.75        10
          30       0.88      0.74      0.80        68
          31       0.94      0.96      0.95       359
          33       0.00      0.00      0.00         1
          35       1.00      1.00      1.00         8
          36       1.00      0.25      0.40         4
          37       0.90      0.91      0.91       767
          38       0.67      0.67      0.67         3
          39       1.00      1.00      1.00         1
          40       1.00      1.00      1.00         1
          41       0.95      0.96      0.96       322
          42       0.82      1.00      0.90        14

    accuracy                           0.91      3000
   macro avg       0.86      0.76      0.79      3000
weighted avg       0.91      0.91      0.91      3000

Model summary saved to model_summary_xgboost_interaction_type_20250602_030635.txt
Model saved to saved_models/xgboost_interaction_type_20250602_030635.pkl

Training XGBoost for Severity Level...
Training completed in 3.35 seconds
XGBoost - Severity Level Accuracy: 0.9773
XGBoost - Severity Level F1 Score: 0.9773

Classification Report for XGBoost - Severity Level:
              precision    recall  f1-score   support

           0       0.97      0.97      0.97       905
           1       0.98      0.95      0.96       365
           2       0.98      0.99      0.98      1730

    accuracy                           0.98      3000
   macro avg       0.98      0.97      0.97      3000
weighted avg       0.98      0.98      0.98      3000

Model summary saved to model_summary_xgboost_severity_level_20250602_030638.txt
Model saved to saved_models/xgboost_severity_level_20250602_030638.pkl

5.5 LightGBM
------------------------------
Current LightGBM parameters:
  boosting_type: gbdt
  num_leaves: 31
  max_depth: -1
  learning_rate: 0.1
  n_estimators: 200
  subsample_for_bin: 200000
  objective: multiclass
  class_weight: None
  min_split_gain: 0.0
  min_child_weight: 0.001
  min_child_samples: 20
  subsample: 1.0
  subsample_freq: 0
  colsample_bytree: 1.0
  reg_alpha: 0.0
  reg_lambda: 0.0
  random_state: 42
  n_jobs: -1
  silent: True
  importance_type: split
Cleaned LightGBM parameters (removed None values):
  boosting_type: gbdt
  num_leaves: 31
  max_depth: -1
  learning_rate: 0.1
  n_estimators: 200
  subsample_for_bin: 200000
  objective: multiclass
  min_split_gain: 0.0
  min_child_weight: 0.001
  min_child_samples: 20
  subsample: 1.0
  subsample_freq: 0
  colsample_bytree: 1.0
  reg_alpha: 0.0
  reg_lambda: 0.0
  random_state: 42
  n_jobs: -1
  silent: True
  importance_type: split

Training LightGBM for Interaction Type...
[LightGBM] [Warning] Unknown parameter: silent
[LightGBM] [Warning] Unknown parameter: silent
[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.007285 seconds.
You can set `force_row_wise=true` to remove the overhead.
And if memory is not enough, you can set `force_col_wise=true`.
[LightGBM] [Info] Total Bins 905
[LightGBM] [Info] Number of data points in the train set: 12000, number of used features: 151
[LightGBM] [Info] Start training from score -1.970288
[LightGBM] [Info] Start training from score -4.892852
[LightGBM] [Info] Start training from score -7.783224
[LightGBM] [Info] Start training from score -4.998213
[LightGBM] [Info] Start training from score -5.035953
[LightGBM] [Info] Start training from score -6.907755
[LightGBM] [Info] Start training from score -6.827713
[LightGBM] [Info] Start training from score -8.294050
[LightGBM] [Info] Start training from score -4.817951
[LightGBM] [Info] Start training from score -6.301619
[LightGBM] [Info] Start training from score -7.313220
[LightGBM] [Info] Start training from score -6.994767
[LightGBM] [Info] Start training from score -3.442019
[LightGBM] [Info] Start training from score -8.006368
[LightGBM] [Info] Start training from score -8.006368
[LightGBM] [Info] Start training from score -9.392662
[LightGBM] [Info] Start training from score -9.392662
[LightGBM] [Info] Start training from score -7.446752
[LightGBM] [Info] Start training from score -7.446752
[LightGBM] [Info] Start training from score -3.985490
[LightGBM] [Info] Start training from score -3.509340
[LightGBM] [Info] Start training from score -4.904026
[LightGBM] [Info] Start training from score -4.104395
[LightGBM] [Info] Start training from score -1.694179
[LightGBM] [Info] Start training from score -9.392662
[LightGBM] [Info] Start training from score -6.257168
[LightGBM] [Info] Start training from score -6.684612
[LightGBM] [Info] Start training from score -9.392662
[LightGBM] [Info] Start training from score -6.257168
[LightGBM] [Info] Start training from score -9.392662
[LightGBM] [Info] Start training from score -3.761450
[LightGBM] [Info] Start training from score -2.160652
[LightGBM] [Info] Start training from score -8.699515
[LightGBM] [Info] Start training from score -7.600902
[LightGBM] [Info] Start training from score -9.392662
[LightGBM] [Info] Start training from score -6.096825
[LightGBM] [Info] Start training from score -6.301619
[LightGBM] [Info] Start training from score -1.314904
[LightGBM] [Info] Start training from score -6.348139
[LightGBM] [Info] Start training from score -6.907755
[LightGBM] [Info] Start training from score -7.313220
[LightGBM] [Info] Start training from score -2.198225
[LightGBM] [Info] Start training from score -5.608472
[LightGBM] [Info] Start training from score -8.699515
[LightGBM] [Warning] No further splits with positive gain, best gain: -inf
[LightGBM] [Warning] No further splits with positive gain, best gain: -inf
[LightGBM] [Warning] No further splits with positive gain, best gain: -inf
[LightGBM] [Warning] No further splits with positive gain, best gain: -inf
[LightGBM] [Warning] No further splits with positive gain, best gain: -inf
[LightGBM] [Warning] No further splits with positive gain, best gain: -inf
[LightGBM] [Warning] No further splits with positive gain, best gain: -inf
[LightGBM] [Warning] No further splits with positive gain, best gain: -inf
[LightGBM] [Warning] No further splits with positive gain, best gain: -inf
[LightGBM] [Warning] No further splits with positive gain, best gain: -inf
[LightGBM] [Warning] No further splits with positive gain, best gain: -inf
[LightGBM] [Warning] No further splits with positive gain, best gain: -inf
[LightGBM] [Warning] No further splits with positive gain, best gain: -inf
[LightGBM] [Warning] Stopped training because there are no more leaves that meet the split requirements
Training completed in 3.73 seconds
[LightGBM] [Warning] Unknown parameter: silent
LightGBM - Interaction Type Accuracy: 0.1947
LightGBM - Interaction Type F1 Score: 0.1541

Classification Report for LightGBM - Interaction Type:
              precision    recall  f1-score   support

           0       0.21      0.03      0.05       442
           1       1.00      0.40      0.57        20
           2       0.00      0.00      0.00         1
           3       0.00      0.00      0.00        22
           4       0.00      0.00      0.00        18
           5       0.00      0.00      0.00         4
           6       0.00      0.00      0.00         2
           7       0.00      0.00      0.00         1
           8       0.00      0.00      0.00        21
           9       0.00      0.00      0.00         3
          10       0.00      0.00      0.00         2
          11       0.00      0.00      0.00         4
          12       0.00      0.00      0.00        93
          13       0.00      0.00      0.00         1
          19       0.08      0.10      0.09        58
          20       0.00      0.00      0.00       109
          21       0.00      0.00      0.00        16
          22       0.00      0.00      0.00        38
          23       0.20      0.56      0.29       581
          25       0.00      0.00      0.00         4
          26       0.00      0.00      0.00         2
          28       0.00      0.00      0.00        10
          30       0.00      0.00      0.00        68
          31       0.21      0.17      0.19       359
          33       0.00      0.00      0.00         1
          35       0.00      0.00      0.00         8
          36       0.00      0.00      0.00         4
          37       0.21      0.20      0.20       767
          38       0.00      0.00      0.00         3
          39       0.00      0.00      0.00         1
          40       0.00      0.00      0.00         1
          41       0.25      0.06      0.09       322
          42       0.00      0.00      0.00        14

    accuracy                           0.19      3000
   macro avg       0.07      0.05      0.05      3000
weighted avg       0.18      0.19      0.15      3000

Model summary saved to model_summary_lightgbm_interaction_type_20250602_030642.txt
Model saved to saved_models/lightgbm_interaction_type_20250602_030642.pkl

Training LightGBM for Severity Level...
[LightGBM] [Warning] Unknown parameter: silent
[LightGBM] [Warning] Unknown parameter: silent
[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.005595 seconds.
You can set `force_row_wise=true` to remove the overhead.
And if memory is not enough, you can set `force_col_wise=true`.
[LightGBM] [Info] Total Bins 905
[LightGBM] [Info] Number of data points in the train set: 12000, number of used features: 151
[LightGBM] [Info] Start training from score -1.205363
[LightGBM] [Info] Start training from score -2.064881
[LightGBM] [Info] Start training from score -0.555852
Training completed in 1.00 seconds
[LightGBM] [Warning] Unknown parameter: silent
LightGBM - Severity Level Accuracy: 0.9710
LightGBM - Severity Level F1 Score: 0.9709

Classification Report for LightGBM - Severity Level:
              precision    recall  f1-score   support

           0       0.97      0.95      0.96       905
           1       0.98      0.93      0.95       365
           2       0.97      0.99      0.98      1730

    accuracy                           0.97      3000
   macro avg       0.97      0.96      0.97      3000
weighted avg       0.97      0.97      0.97      3000

Model summary saved to model_summary_lightgbm_severity_level_20250602_030644.txt
Model saved to saved_models/lightgbm_severity_level_20250602_030644.pkl

5.6 Deep Learning Models
------------------------------

5.6.1 CNN Models
--------------------
Creating CNN model...
Current CNN parameters:
  filters_1: 64
  filters_2: 32
  filters_3: 16
  kernel_size: 3
  pool_size: 2
  dropout_conv: 0.3
  dropout_dense: 0.5
  dense_units_1: 128
  dense_units_2: 64
  learning_rate: 0.001
  batch_size: 32
  epochs: 50
  validation_split: 0.2
  early_stopping_patience: 15
  reduce_lr_patience: 10
  reduce_lr_factor: 0.5
  min_lr: 1e-07
I0000 00:00:1748833604.380549      35 gpu_device.cc:2022] Created device /job:localhost/replica:0/task:0/device:GPU:0 with 13942 MB memory:  -> device: 0, name: Tesla T4, pci bus id: 0000:00:04.0, compute capability: 7.5
I0000 00:00:1748833604.381195      35 gpu_device.cc:2022] Created device /job:localhost/replica:0/task:0/device:GPU:1 with 13942 MB memory:  -> device: 1, name: Tesla T4, pci bus id: 0000:00:05.0, compute capability: 7.5

Training CNN for Interaction Type...
Epoch 1/50
WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
I0000 00:00:1748833608.984424     111 service.cc:148] XLA service 0x7bf23c033230 initialized for platform CUDA (this does not guarantee that XLA will be used). Devices:
I0000 00:00:1748833608.985067     111 service.cc:156]   StreamExecutor device (0): Tesla T4, Compute Capability 7.5
I0000 00:00:1748833608.985086     111 service.cc:156]   StreamExecutor device (1): Tesla T4, Compute Capability 7.5
I0000 00:00:1748833609.356517     111 cuda_dnn.cc:529] Loaded cuDNN version 90300
 36/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.0758 - loss: 3.6483
I0000 00:00:1748833613.412951     111 device_compiler.h:188] Compiled cluster using XLA!  This line is logged at most once for the lifetime of the process.
375/375 ━━━━━━━━━━━━━━━━━━━━ 11s 8ms/step - accuracy: 0.2005 - loss: 2.8136 - val_accuracy: 0.2557 - val_loss: 2.2888 - learning_rate: 0.0010
Epoch 2/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.2447 - loss: 2.2843 - val_accuracy: 0.2940 - val_loss: 2.1586 - learning_rate: 0.0010
Epoch 3/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.2872 - loss: 2.1629 - val_accuracy: 0.2937 - val_loss: 2.0485 - learning_rate: 0.0010
Epoch 4/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.2964 - loss: 2.1145 - val_accuracy: 0.3057 - val_loss: 1.9953 - learning_rate: 0.0010
Epoch 5/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.3033 - loss: 2.0739 - val_accuracy: 0.3167 - val_loss: 1.9619 - learning_rate: 0.0010
Epoch 6/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.3039 - loss: 2.0422 - val_accuracy: 0.3153 - val_loss: 1.9260 - learning_rate: 0.0010
Epoch 7/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.3067 - loss: 2.0147 - val_accuracy: 0.3280 - val_loss: 1.9012 - learning_rate: 0.0010
Epoch 8/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.3302 - loss: 1.9960 - val_accuracy: 0.3453 - val_loss: 1.8747 - learning_rate: 0.0010
Epoch 9/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.3270 - loss: 1.9778 - val_accuracy: 0.3530 - val_loss: 1.8615 - learning_rate: 0.0010
Epoch 10/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.3345 - loss: 1.9460 - val_accuracy: 0.3667 - val_loss: 1.8074 - learning_rate: 0.0010
Epoch 11/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.3575 - loss: 1.9100 - val_accuracy: 0.3840 - val_loss: 1.7770 - learning_rate: 0.0010
Epoch 12/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.3632 - loss: 1.8879 - val_accuracy: 0.3903 - val_loss: 1.7491 - learning_rate: 0.0010
Epoch 13/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.3669 - loss: 1.8680 - val_accuracy: 0.3973 - val_loss: 1.7274 - learning_rate: 0.0010
Epoch 14/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.3660 - loss: 1.8435 - val_accuracy: 0.3940 - val_loss: 1.7132 - learning_rate: 0.0010
Epoch 15/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.3764 - loss: 1.8229 - val_accuracy: 0.4087 - val_loss: 1.6910 - learning_rate: 0.0010
Epoch 16/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.3893 - loss: 1.8099 - val_accuracy: 0.4090 - val_loss: 1.6694 - learning_rate: 0.0010
Epoch 17/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.3884 - loss: 1.7939 - val_accuracy: 0.4103 - val_loss: 1.6548 - learning_rate: 0.0010
Epoch 18/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.3880 - loss: 1.7840 - val_accuracy: 0.4177 - val_loss: 1.6394 - learning_rate: 0.0010
Epoch 19/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.3939 - loss: 1.7684 - val_accuracy: 0.4243 - val_loss: 1.6173 - learning_rate: 0.0010
Epoch 20/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4044 - loss: 1.7515 - val_accuracy: 0.4370 - val_loss: 1.6063 - learning_rate: 0.0010
Epoch 21/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.4103 - loss: 1.7288 - val_accuracy: 0.4437 - val_loss: 1.5690 - learning_rate: 0.0010
Epoch 22/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4133 - loss: 1.7149 - val_accuracy: 0.4493 - val_loss: 1.5427 - learning_rate: 0.0010
Epoch 23/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4179 - loss: 1.6873 - val_accuracy: 0.4530 - val_loss: 1.5198 - learning_rate: 0.0010
Epoch 24/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4270 - loss: 1.6569 - val_accuracy: 0.4637 - val_loss: 1.5124 - learning_rate: 0.0010
Epoch 25/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4312 - loss: 1.6527 - val_accuracy: 0.4687 - val_loss: 1.5022 - learning_rate: 0.0010
Epoch 26/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4461 - loss: 1.6356 - val_accuracy: 0.4677 - val_loss: 1.4839 - learning_rate: 0.0010
Epoch 27/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4462 - loss: 1.6308 - val_accuracy: 0.4743 - val_loss: 1.4710 - learning_rate: 0.0010
Epoch 28/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4417 - loss: 1.6160 - val_accuracy: 0.4730 - val_loss: 1.4664 - learning_rate: 0.0010
Epoch 29/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4503 - loss: 1.6039 - val_accuracy: 0.4797 - val_loss: 1.4473 - learning_rate: 0.0010
Epoch 30/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4511 - loss: 1.5843 - val_accuracy: 0.4783 - val_loss: 1.4389 - learning_rate: 0.0010
Epoch 31/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4557 - loss: 1.5840 - val_accuracy: 0.4843 - val_loss: 1.4489 - learning_rate: 0.0010
Epoch 32/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4616 - loss: 1.5741 - val_accuracy: 0.4820 - val_loss: 1.4228 - learning_rate: 0.0010
Epoch 33/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4566 - loss: 1.5733 - val_accuracy: 0.4883 - val_loss: 1.4191 - learning_rate: 0.0010
Epoch 34/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4630 - loss: 1.5561 - val_accuracy: 0.4833 - val_loss: 1.4115 - learning_rate: 0.0010
Epoch 35/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4671 - loss: 1.5509 - val_accuracy: 0.4817 - val_loss: 1.4029 - learning_rate: 0.0010
Epoch 36/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4670 - loss: 1.5475 - val_accuracy: 0.4913 - val_loss: 1.3966 - learning_rate: 0.0010
Epoch 37/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4739 - loss: 1.5281 - val_accuracy: 0.4957 - val_loss: 1.3853 - learning_rate: 0.0010
Epoch 38/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4755 - loss: 1.5165 - val_accuracy: 0.4967 - val_loss: 1.3918 - learning_rate: 0.0010
Epoch 39/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4625 - loss: 1.5272 - val_accuracy: 0.4973 - val_loss: 1.3716 - learning_rate: 0.0010
Epoch 40/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.4830 - loss: 1.4997 - val_accuracy: 0.5073 - val_loss: 1.3586 - learning_rate: 0.0010
Epoch 41/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4791 - loss: 1.5047 - val_accuracy: 0.5100 - val_loss: 1.3627 - learning_rate: 0.0010
Epoch 42/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4793 - loss: 1.5054 - val_accuracy: 0.5120 - val_loss: 1.3602 - learning_rate: 0.0010
Epoch 43/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4772 - loss: 1.4964 - val_accuracy: 0.5157 - val_loss: 1.3448 - learning_rate: 0.0010
Epoch 44/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4789 - loss: 1.4982 - val_accuracy: 0.5103 - val_loss: 1.3478 - learning_rate: 0.0010
Epoch 45/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4869 - loss: 1.4695 - val_accuracy: 0.5153 - val_loss: 1.3343 - learning_rate: 0.0010
Epoch 46/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.4853 - loss: 1.4787 - val_accuracy: 0.5233 - val_loss: 1.3388 - learning_rate: 0.0010
Epoch 47/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.4874 - loss: 1.4813 - val_accuracy: 0.5280 - val_loss: 1.3211 - learning_rate: 0.0010
Epoch 48/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.4810 - loss: 1.4736 - val_accuracy: 0.5307 - val_loss: 1.3076 - learning_rate: 0.0010
Epoch 49/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.4919 - loss: 1.4557 - val_accuracy: 0.5297 - val_loss: 1.3107 - learning_rate: 0.0010
Epoch 50/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.4906 - loss: 1.4609 - val_accuracy: 0.5317 - val_loss: 1.3175 - learning_rate: 0.0010
Restoring model weights from the end of the best epoch: 48.
Training completed in 77.01 seconds
94/94 ━━━━━━━━━━━━━━━━━━━━ 1s 5ms/step
CNN - Interaction Type Accuracy: 0.5307
CNN - Interaction Type F1 Score: 0.4976

Classification Report for CNN - Interaction Type:
              precision    recall  f1-score   support

           0       0.43      0.58      0.50       442
           1       0.88      0.70      0.78        20
           2       0.00      0.00      0.00         1
           3       0.65      0.50      0.56        22
           4       1.00      0.17      0.29        18
           5       0.00      0.00      0.00         4
           6       0.00      0.00      0.00         2
           7       0.00      0.00      0.00         1
           8       1.00      1.00      1.00        21
           9       0.00      0.00      0.00         3
          10       0.00      0.00      0.00         2
          11       0.00      0.00      0.00         4
          12       0.70      0.32      0.44        93
          13       0.00      0.00      0.00         1
          19       1.00      0.21      0.34        58
          20       0.88      0.72      0.79       109
          21       0.00      0.00      0.00        16
          22       0.77      0.45      0.57        38
          23       0.48      0.20      0.28       581
          25       0.00      0.00      0.00         4
          26       0.00      0.00      0.00         2
          28       0.00      0.00      0.00        10
          30       0.00      0.00      0.00        68
          31       0.61      0.53      0.57       359
          33       0.00      0.00      0.00         1
          35       0.00      0.00      0.00         8
          36       0.00      0.00      0.00         4
          37       0.47      0.81      0.59       767
          38       0.00      0.00      0.00         3
          39       0.00      0.00      0.00         1
          40       0.00      0.00      0.00         1
          41       0.73      0.68      0.71       322
          42       0.00      0.00      0.00        14

    accuracy                           0.53      3000
   macro avg       0.29      0.21      0.22      3000
weighted avg       0.53      0.53      0.50      3000

Model summary saved to model_summary_cnn_interaction_type_20250602_030803.txt
Keras model saved to saved_models/cnn_interaction_type_20250602_030803.h5
Metadata saved to saved_models/cnn_interaction_type_20250602_030803_metadata.json
Creating CNN model...
Current CNN parameters:
  filters_1: 64
  filters_2: 32
  filters_3: 16
  kernel_size: 3
  pool_size: 2
  dropout_conv: 0.3
  dropout_dense: 0.5
  dense_units_1: 128
  dense_units_2: 64
  learning_rate: 0.001
  batch_size: 32
  epochs: 50
  validation_split: 0.2
  early_stopping_patience: 15
  reduce_lr_patience: 10
  reduce_lr_factor: 0.5
  min_lr: 1e-07

Training CNN for Severity Level...
Epoch 1/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 8s 6ms/step - accuracy: 0.5662 - loss: 0.9929 - val_accuracy: 0.5850 - val_loss: 0.8687 - learning_rate: 0.0010
Epoch 2/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.5820 - loss: 0.8817 - val_accuracy: 0.5850 - val_loss: 0.8333 - learning_rate: 0.0010
Epoch 3/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.5871 - loss: 0.8584 - val_accuracy: 0.5820 - val_loss: 0.8216 - learning_rate: 0.0010
Epoch 4/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.5839 - loss: 0.8454 - val_accuracy: 0.5837 - val_loss: 0.8130 - learning_rate: 0.0010
Epoch 5/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.5899 - loss: 0.8359 - val_accuracy: 0.6257 - val_loss: 0.7910 - learning_rate: 0.0010
Epoch 6/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6001 - loss: 0.8161 - val_accuracy: 0.6233 - val_loss: 0.7578 - learning_rate: 0.0010
Epoch 7/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6226 - loss: 0.7899 - val_accuracy: 0.6287 - val_loss: 0.7533 - learning_rate: 0.0010
Epoch 8/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6255 - loss: 0.7891 - val_accuracy: 0.6530 - val_loss: 0.7374 - learning_rate: 0.0010
Epoch 9/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6361 - loss: 0.7753 - val_accuracy: 0.6637 - val_loss: 0.7248 - learning_rate: 0.0010
Epoch 10/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6495 - loss: 0.7569 - val_accuracy: 0.6610 - val_loss: 0.7170 - learning_rate: 0.0010
Epoch 11/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6465 - loss: 0.7588 - val_accuracy: 0.6640 - val_loss: 0.7076 - learning_rate: 0.0010
Epoch 12/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6553 - loss: 0.7475 - val_accuracy: 0.6743 - val_loss: 0.7001 - learning_rate: 0.0010
Epoch 13/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6531 - loss: 0.7413 - val_accuracy: 0.6833 - val_loss: 0.6926 - learning_rate: 0.0010
Epoch 14/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6492 - loss: 0.7449 - val_accuracy: 0.6807 - val_loss: 0.6824 - learning_rate: 0.0010
Epoch 15/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6630 - loss: 0.7282 - val_accuracy: 0.6933 - val_loss: 0.6711 - learning_rate: 0.0010
Epoch 16/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6626 - loss: 0.7269 - val_accuracy: 0.6810 - val_loss: 0.6709 - learning_rate: 0.0010
Epoch 17/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6656 - loss: 0.7255 - val_accuracy: 0.7023 - val_loss: 0.6548 - learning_rate: 0.0010
Epoch 18/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6658 - loss: 0.7150 - val_accuracy: 0.7027 - val_loss: 0.6526 - learning_rate: 0.0010
Epoch 19/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6671 - loss: 0.7115 - val_accuracy: 0.7093 - val_loss: 0.6447 - learning_rate: 0.0010
Epoch 20/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6778 - loss: 0.7012 - val_accuracy: 0.7077 - val_loss: 0.6398 - learning_rate: 0.0010
Epoch 21/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6737 - loss: 0.6969 - val_accuracy: 0.7030 - val_loss: 0.6386 - learning_rate: 0.0010
Epoch 22/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6775 - loss: 0.7019 - val_accuracy: 0.7110 - val_loss: 0.6230 - learning_rate: 0.0010
Epoch 23/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6871 - loss: 0.6905 - val_accuracy: 0.7077 - val_loss: 0.6262 - learning_rate: 0.0010
Epoch 24/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6812 - loss: 0.6856 - val_accuracy: 0.7073 - val_loss: 0.6161 - learning_rate: 0.0010
Epoch 25/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6921 - loss: 0.6801 - val_accuracy: 0.7103 - val_loss: 0.6170 - learning_rate: 0.0010
Epoch 26/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6918 - loss: 0.6753 - val_accuracy: 0.7160 - val_loss: 0.6187 - learning_rate: 0.0010
Epoch 27/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6875 - loss: 0.6731 - val_accuracy: 0.7127 - val_loss: 0.6152 - learning_rate: 0.0010
Epoch 28/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.6897 - loss: 0.6745 - val_accuracy: 0.7153 - val_loss: 0.6127 - learning_rate: 0.0010
Epoch 29/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6891 - loss: 0.6677 - val_accuracy: 0.7190 - val_loss: 0.6100 - learning_rate: 0.0010
Epoch 30/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6902 - loss: 0.6716 - val_accuracy: 0.7233 - val_loss: 0.5975 - learning_rate: 0.0010
Epoch 31/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.7032 - loss: 0.6585 - val_accuracy: 0.7213 - val_loss: 0.6067 - learning_rate: 0.0010
Epoch 32/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6968 - loss: 0.6583 - val_accuracy: 0.7307 - val_loss: 0.5942 - learning_rate: 0.0010
Epoch 33/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.6949 - loss: 0.6627 - val_accuracy: 0.7287 - val_loss: 0.5928 - learning_rate: 0.0010
Epoch 34/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.7021 - loss: 0.6654 - val_accuracy: 0.7310 - val_loss: 0.5889 - learning_rate: 0.0010
Epoch 35/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.7050 - loss: 0.6515 - val_accuracy: 0.7370 - val_loss: 0.5803 - learning_rate: 0.0010
Epoch 36/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.7130 - loss: 0.6457 - val_accuracy: 0.7297 - val_loss: 0.5905 - learning_rate: 0.0010
Epoch 37/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.7105 - loss: 0.6474 - val_accuracy: 0.7360 - val_loss: 0.5826 - learning_rate: 0.0010
Epoch 38/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.7063 - loss: 0.6453 - val_accuracy: 0.7477 - val_loss: 0.5787 - learning_rate: 0.0010
Epoch 39/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.7082 - loss: 0.6467 - val_accuracy: 0.7453 - val_loss: 0.5730 - learning_rate: 0.0010
Epoch 40/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.7124 - loss: 0.6372 - val_accuracy: 0.7483 - val_loss: 0.5733 - learning_rate: 0.0010
Epoch 41/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.7119 - loss: 0.6373 - val_accuracy: 0.7533 - val_loss: 0.5647 - learning_rate: 0.0010
Epoch 42/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.7176 - loss: 0.6327 - val_accuracy: 0.7530 - val_loss: 0.5594 - learning_rate: 0.0010
Epoch 43/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.7154 - loss: 0.6324 - val_accuracy: 0.7520 - val_loss: 0.5627 - learning_rate: 0.0010
Epoch 44/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.7198 - loss: 0.6298 - val_accuracy: 0.7580 - val_loss: 0.5545 - learning_rate: 0.0010
Epoch 45/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.7114 - loss: 0.6349 - val_accuracy: 0.7510 - val_loss: 0.5563 - learning_rate: 0.0010
Epoch 46/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.7168 - loss: 0.6235 - val_accuracy: 0.7583 - val_loss: 0.5509 - learning_rate: 0.0010
Epoch 47/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.7195 - loss: 0.6236 - val_accuracy: 0.7647 - val_loss: 0.5444 - learning_rate: 0.0010
Epoch 48/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.7160 - loss: 0.6274 - val_accuracy: 0.7680 - val_loss: 0.5515 - learning_rate: 0.0010
Epoch 49/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step - accuracy: 0.7175 - loss: 0.6272 - val_accuracy: 0.7687 - val_loss: 0.5394 - learning_rate: 0.0010
Epoch 50/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.7233 - loss: 0.6160 - val_accuracy: 0.7607 - val_loss: 0.5377 - learning_rate: 0.0010
Restoring model weights from the end of the best epoch: 50.
Training completed in 74.98 seconds
94/94 ━━━━━━━━━━━━━━━━━━━━ 1s 5ms/step
CNN - Severity Level Accuracy: 0.7607
CNN - Severity Level F1 Score: 0.7571

Classification Report for CNN - Severity Level:
              precision    recall  f1-score   support

           0       0.70      0.68      0.69       905
           1       0.77      0.55      0.64       365
           2       0.79      0.85      0.82      1730

    accuracy                           0.76      3000
   macro avg       0.75      0.69      0.72      3000
weighted avg       0.76      0.76      0.76      3000

Model summary saved to model_summary_cnn_severity_level_20250602_030919.txt
Keras model saved to saved_models/cnn_severity_level_20250602_030919.h5
Metadata saved to saved_models/cnn_severity_level_20250602_030919_metadata.json

5.6.2 LSTM Models
--------------------
Creating LSTM model...
Current LSTM parameters:
  lstm_units_1: 128
  lstm_units_2: 64
  dropout: 0.3
  recurrent_dropout: 0.3
  dense_units_1: 128
  dense_units_2: 64
  dropout_dense: 0.5
  learning_rate: 0.001
  batch_size: 32
  epochs: 50
  validation_split: 0.2
  early_stopping_patience: 15
  reduce_lr_patience: 10
  reduce_lr_factor: 0.5
  min_lr: 1e-07

Training LSTM for Interaction Type...
Epoch 1/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 361s 938ms/step - accuracy: 0.2008 - loss: 2.7032 - val_accuracy: 0.2557 - val_loss: 2.2033 - learning_rate: 0.0010
Epoch 2/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 350s 935ms/step - accuracy: 0.2401 - loss: 2.3079 - val_accuracy: 0.2557 - val_loss: 2.2018 - learning_rate: 0.0010
Epoch 3/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 350s 934ms/step - accuracy: 0.2576 - loss: 2.2724 - val_accuracy: 0.2557 - val_loss: 2.1988 - learning_rate: 0.0010
Epoch 4/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 351s 936ms/step - accuracy: 0.2652 - loss: 2.2613 - val_accuracy: 0.2557 - val_loss: 2.1948 - learning_rate: 0.0010
Epoch 5/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 351s 937ms/step - accuracy: 0.2648 - loss: 2.2485 - val_accuracy: 0.2557 - val_loss: 2.1874 - learning_rate: 0.0010
Epoch 6/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 351s 936ms/step - accuracy: 0.2651 - loss: 2.2429 - val_accuracy: 0.2557 - val_loss: 2.1856 - learning_rate: 0.0010
Epoch 7/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 351s 936ms/step - accuracy: 0.2660 - loss: 2.2432 - val_accuracy: 0.2557 - val_loss: 2.1864 - learning_rate: 0.0010
Epoch 8/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 352s 938ms/step - accuracy: 0.2656 - loss: 2.2356 - val_accuracy: 0.2557 - val_loss: 2.1733 - learning_rate: 0.0010
Epoch 9/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 351s 935ms/step - accuracy: 0.2649 - loss: 2.2298 - val_accuracy: 0.2557 - val_loss: 2.1716 - learning_rate: 0.0010
Epoch 10/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 351s 936ms/step - accuracy: 0.2656 - loss: 2.2292 - val_accuracy: 0.2557 - val_loss: 2.2009 - learning_rate: 0.0010
Epoch 11/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 350s 935ms/step - accuracy: 0.2656 - loss: 2.2496 - val_accuracy: 0.2557 - val_loss: 2.2010 - learning_rate: 0.0010
Epoch 12/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 350s 934ms/step - accuracy: 0.2656 - loss: 2.2438 - val_accuracy: 0.2557 - val_loss: 2.2009 - learning_rate: 0.0010
Epoch 13/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 350s 933ms/step - accuracy: 0.2656 - loss: 2.2461 - val_accuracy: 0.2557 - val_loss: 2.2007 - learning_rate: 0.0010
Epoch 14/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 350s 934ms/step - accuracy: 0.2656 - loss: 2.2453 - val_accuracy: 0.2557 - val_loss: 2.2003 - learning_rate: 0.0010
Epoch 15/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 350s 934ms/step - accuracy: 0.2656 - loss: 2.2461 - val_accuracy: 0.2557 - val_loss: 2.1996 - learning_rate: 0.0010
Epoch 16/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 349s 932ms/step - accuracy: 0.2656 - loss: 2.2401 - val_accuracy: 0.2557 - val_loss: 2.1992 - learning_rate: 0.0010
Epoch 17/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 929ms/step - accuracy: 0.2656 - loss: 2.2383 - val_accuracy: 0.2557 - val_loss: 2.1995 - learning_rate: 0.0010
Epoch 18/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 349s 931ms/step - accuracy: 0.2656 - loss: 2.2420 - val_accuracy: 0.2557 - val_loss: 2.1995 - learning_rate: 0.0010
Epoch 19/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 0s 857ms/step - accuracy: 0.2656 - loss: 2.2387
Epoch 19: ReduceLROnPlateau reducing learning rate to 0.0005000000237487257.
375/375 ━━━━━━━━━━━━━━━━━━━━ 349s 931ms/step - accuracy: 0.2656 - loss: 2.2387 - val_accuracy: 0.2557 - val_loss: 2.1992 - learning_rate: 0.0010
Epoch 20/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 929ms/step - accuracy: 0.2656 - loss: 2.2424 - val_accuracy: 0.2557 - val_loss: 2.1979 - learning_rate: 5.0000e-04
Epoch 21/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 929ms/step - accuracy: 0.2656 - loss: 2.2390 - val_accuracy: 0.2557 - val_loss: 2.1978 - learning_rate: 5.0000e-04
Epoch 22/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 349s 930ms/step - accuracy: 0.2656 - loss: 2.2392 - val_accuracy: 0.2557 - val_loss: 2.1980 - learning_rate: 5.0000e-04
Epoch 23/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 349s 930ms/step - accuracy: 0.2656 - loss: 2.2396 - val_accuracy: 0.2557 - val_loss: 2.1979 - learning_rate: 5.0000e-04
Epoch 24/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 349s 931ms/step - accuracy: 0.2656 - loss: 2.2401 - val_accuracy: 0.2557 - val_loss: 2.1978 - learning_rate: 5.0000e-04
Epoch 24: early stopping
Restoring model weights from the end of the best epoch: 9.
Training completed in 8409.16 seconds
94/94 ━━━━━━━━━━━━━━━━━━━━ 29s 302ms/step
LSTM - Interaction Type Accuracy: 0.2557
LSTM - Interaction Type F1 Score: 0.1041

Classification Report for LSTM - Interaction Type:
              precision    recall  f1-score   support

           0       0.00      0.00      0.00       442
           1       0.00      0.00      0.00        20
           2       0.00      0.00      0.00         1
           3       0.00      0.00      0.00        22
           4       0.00      0.00      0.00        18
           5       0.00      0.00      0.00         4
           6       0.00      0.00      0.00         2
           7       0.00      0.00      0.00         1
           8       0.00      0.00      0.00        21
           9       0.00      0.00      0.00         3
          10       0.00      0.00      0.00         2
          11       0.00      0.00      0.00         4
          12       0.00      0.00      0.00        93
          13       0.00      0.00      0.00         1
          19       0.00      0.00      0.00        58
          20       0.00      0.00      0.00       109
          21       0.00      0.00      0.00        16
          22       0.00      0.00      0.00        38
          23       0.00      0.00      0.00       581
          25       0.00      0.00      0.00         4
          26       0.00      0.00      0.00         2
          28       0.00      0.00      0.00        10
          30       0.00      0.00      0.00        68
          31       0.00      0.00      0.00       359
          33       0.00      0.00      0.00         1
          35       0.00      0.00      0.00         8
          36       0.00      0.00      0.00         4
          37       0.26      1.00      0.41       767
          38       0.00      0.00      0.00         3
          39       0.00      0.00      0.00         1
          40       0.00      0.00      0.00         1
          41       0.00      0.00      0.00       322
          42       0.00      0.00      0.00        14

    accuracy                           0.26      3000
   macro avg       0.01      0.03      0.01      3000
weighted avg       0.07      0.26      0.10      3000

Model summary saved to model_summary_lstm_interaction_type_20250602_052958.txt
Keras model saved to saved_models/lstm_interaction_type_20250602_052958.h5
Metadata saved to saved_models/lstm_interaction_type_20250602_052958_metadata.json
Creating LSTM model...
Current LSTM parameters:
  lstm_units_1: 128
  lstm_units_2: 64
  dropout: 0.3
  recurrent_dropout: 0.3
  dense_units_1: 128
  dense_units_2: 64
  dropout_dense: 0.5
  learning_rate: 0.001
  batch_size: 32
  epochs: 50
  validation_split: 0.2
  early_stopping_patience: 15
  reduce_lr_patience: 10
  reduce_lr_factor: 0.5
  min_lr: 1e-07

Training LSTM for Severity Level...
Epoch 1/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 356s 931ms/step - accuracy: 0.5697 - loss: 0.9726 - val_accuracy: 0.5767 - val_loss: 0.9429 - learning_rate: 0.0010
Epoch 2/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 929ms/step - accuracy: 0.5758 - loss: 0.9550 - val_accuracy: 0.5767 - val_loss: 0.9374 - learning_rate: 0.0010
Epoch 3/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 349s 930ms/step - accuracy: 0.5758 - loss: 0.9510 - val_accuracy: 0.5767 - val_loss: 0.9356 - learning_rate: 0.0010
Epoch 4/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 349s 930ms/step - accuracy: 0.5758 - loss: 0.9482 - val_accuracy: 0.5767 - val_loss: 0.9354 - learning_rate: 0.0010
Epoch 5/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 350s 933ms/step - accuracy: 0.5758 - loss: 0.9462 - val_accuracy: 0.5767 - val_loss: 0.9355 - learning_rate: 0.0010
Epoch 6/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 929ms/step - accuracy: 0.5758 - loss: 0.9455 - val_accuracy: 0.5767 - val_loss: 0.9354 - learning_rate: 0.0010
Epoch 7/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 349s 930ms/step - accuracy: 0.5758 - loss: 0.9456 - val_accuracy: 0.5767 - val_loss: 0.9354 - learning_rate: 0.0010
Epoch 8/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 349s 931ms/step - accuracy: 0.5758 - loss: 0.9448 - val_accuracy: 0.5767 - val_loss: 0.9354 - learning_rate: 0.0010
Epoch 9/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 928ms/step - accuracy: 0.5758 - loss: 0.9442 - val_accuracy: 0.5767 - val_loss: 0.9354 - learning_rate: 0.0010
Epoch 10/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 349s 930ms/step - accuracy: 0.5758 - loss: 0.9456 - val_accuracy: 0.5767 - val_loss: 0.9354 - learning_rate: 0.0010
Epoch 11/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 347s 927ms/step - accuracy: 0.5758 - loss: 0.9444 - val_accuracy: 0.5767 - val_loss: 0.9355 - learning_rate: 0.0010
Epoch 12/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 928ms/step - accuracy: 0.5758 - loss: 0.9451 - val_accuracy: 0.5767 - val_loss: 0.9354 - learning_rate: 0.0010
Epoch 13/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 347s 926ms/step - accuracy: 0.5758 - loss: 0.9461 - val_accuracy: 0.5767 - val_loss: 0.9354 - learning_rate: 0.0010
Epoch 14/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 0s 852ms/step - accuracy: 0.5758 - loss: 0.9449
Epoch 14: ReduceLROnPlateau reducing learning rate to 0.0005000000237487257.
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 927ms/step - accuracy: 0.5758 - loss: 0.9449 - val_accuracy: 0.5767 - val_loss: 0.9354 - learning_rate: 0.0010
Epoch 15/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 927ms/step - accuracy: 0.5758 - loss: 0.9447 - val_accuracy: 0.5767 - val_loss: 0.9353 - learning_rate: 5.0000e-04
Epoch 16/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 927ms/step - accuracy: 0.5758 - loss: 0.9444 - val_accuracy: 0.5767 - val_loss: 0.9353 - learning_rate: 5.0000e-04
Epoch 17/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 927ms/step - accuracy: 0.5758 - loss: 0.9452 - val_accuracy: 0.5767 - val_loss: 0.9353 - learning_rate: 5.0000e-04
Epoch 18/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 349s 930ms/step - accuracy: 0.5758 - loss: 0.9442 - val_accuracy: 0.5767 - val_loss: 0.9353 - learning_rate: 5.0000e-04
Epoch 19/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 927ms/step - accuracy: 0.5758 - loss: 0.9448 - val_accuracy: 0.5767 - val_loss: 0.9353 - learning_rate: 5.0000e-04
Epoch 20/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 929ms/step - accuracy: 0.5758 - loss: 0.9452 - val_accuracy: 0.5767 - val_loss: 0.9353 - learning_rate: 5.0000e-04
Epoch 21/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 928ms/step - accuracy: 0.5758 - loss: 0.9448 - val_accuracy: 0.5767 - val_loss: 0.9353 - learning_rate: 5.0000e-04
Epoch 22/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 349s 930ms/step - accuracy: 0.5758 - loss: 0.9446 - val_accuracy: 0.5767 - val_loss: 0.9353 - learning_rate: 5.0000e-04
Epoch 23/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 928ms/step - accuracy: 0.5758 - loss: 0.9439 - val_accuracy: 0.5767 - val_loss: 0.9353 - learning_rate: 5.0000e-04
Epoch 24/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 0s 853ms/step - accuracy: 0.5758 - loss: 0.9451
Epoch 24: ReduceLROnPlateau reducing learning rate to 0.0002500000118743628.
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 928ms/step - accuracy: 0.5758 - loss: 0.9451 - val_accuracy: 0.5767 - val_loss: 0.9353 - learning_rate: 5.0000e-04
Epoch 25/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 929ms/step - accuracy: 0.5758 - loss: 0.9439 - val_accuracy: 0.5767 - val_loss: 0.9353 - learning_rate: 2.5000e-04
Epoch 26/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 349s 931ms/step - accuracy: 0.5758 - loss: 0.9446 - val_accuracy: 0.5767 - val_loss: 0.9353 - learning_rate: 2.5000e-04
Epoch 27/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 349s 932ms/step - accuracy: 0.5758 - loss: 0.9433 - val_accuracy: 0.5767 - val_loss: 0.9353 - learning_rate: 2.5000e-04
Epoch 28/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 349s 932ms/step - accuracy: 0.5758 - loss: 0.9437 - val_accuracy: 0.5767 - val_loss: 0.9353 - learning_rate: 2.5000e-04
Epoch 29/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 349s 932ms/step - accuracy: 0.5758 - loss: 0.9442 - val_accuracy: 0.5767 - val_loss: 0.9353 - learning_rate: 2.5000e-04
Epoch 30/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 929ms/step - accuracy: 0.5758 - loss: 0.9447 - val_accuracy: 0.5767 - val_loss: 0.9353 - learning_rate: 2.5000e-04
Epoch 31/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 929ms/step - accuracy: 0.5758 - loss: 0.9448 - val_accuracy: 0.5767 - val_loss: 0.9353 - learning_rate: 2.5000e-04
Epoch 32/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 928ms/step - accuracy: 0.5758 - loss: 0.9440 - val_accuracy: 0.5767 - val_loss: 0.9353 - learning_rate: 2.5000e-04
Epoch 33/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 929ms/step - accuracy: 0.5758 - loss: 0.9443 - val_accuracy: 0.5767 - val_loss: 0.9353 - learning_rate: 2.5000e-04
Epoch 34/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 0s 853ms/step - accuracy: 0.5758 - loss: 0.9436
Epoch 34: ReduceLROnPlateau reducing learning rate to 0.0001250000059371814.
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 928ms/step - accuracy: 0.5758 - loss: 0.9436 - val_accuracy: 0.5767 - val_loss: 0.9353 - learning_rate: 2.5000e-04
Epoch 35/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 928ms/step - accuracy: 0.5758 - loss: 0.9441 - val_accuracy: 0.5767 - val_loss: 0.9353 - learning_rate: 1.2500e-04
Epoch 36/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 927ms/step - accuracy: 0.5758 - loss: 0.9438 - val_accuracy: 0.5767 - val_loss: 0.9353 - learning_rate: 1.2500e-04
Epoch 37/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 929ms/step - accuracy: 0.5758 - loss: 0.9436 - val_accuracy: 0.5767 - val_loss: 0.9354 - learning_rate: 1.2500e-04
Epoch 38/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 929ms/step - accuracy: 0.5758 - loss: 0.9436 - val_accuracy: 0.5767 - val_loss: 0.9354 - learning_rate: 1.2500e-04
Epoch 39/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 348s 928ms/step - accuracy: 0.5758 - loss: 0.9437 - val_accuracy: 0.5767 - val_loss: 0.9354 - learning_rate: 1.2500e-04
Epoch 39: early stopping
Restoring model weights from the end of the best epoch: 24.
Training completed in 13592.03 seconds
94/94 ━━━━━━━━━━━━━━━━━━━━ 30s 306ms/step
LSTM - Severity Level Accuracy: 0.5767
LSTM - Severity Level F1 Score: 0.4218

Classification Report for LSTM - Severity Level:
              precision    recall  f1-score   support

           0       0.00      0.00      0.00       905
           1       0.00      0.00      0.00       365
           2       0.58      1.00      0.73      1730

    accuracy                           0.58      3000
   macro avg       0.19      0.33      0.24      3000
weighted avg       0.33      0.58      0.42      3000

Model summary saved to model_summary_lstm_severity_level_20250602_091700.txt
Keras model saved to saved_models/lstm_severity_level_20250602_091700.h5
Metadata saved to saved_models/lstm_severity_level_20250602_091700_metadata.json

5.6.3 BERT-Inspired Models
-------------------------
Creating BERT-Inspired model...
Current BERT-Inspired parameters:
  embedding_dim: 256
  attention_heads: 3
  attention_dim: 128
  ff_dim_1: 512
  ff_dim_2: 256
  residual_dim: 256
  final_dense_dim: 128
  dropout_embedding: 0.3
  dropout_attention: 0.3
  dropout_ff: 0.4
  dropout_final: 0.5
  learning_rate: 0.001
  batch_size: 32
  epochs: 50
  validation_split: 0.2
  early_stopping_patience: 15
  reduce_lr_patience: 10
  reduce_lr_factor: 0.5
  min_lr: 1e-07

Training BERT-Inspired for Interaction Type...
Epoch 1/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 9s 6ms/step - accuracy: 0.4042 - loss: 2.1817 - val_accuracy: 0.6937 - val_loss: 0.9223 - learning_rate: 0.0010
Epoch 2/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.6862 - loss: 0.9725 - val_accuracy: 0.7320 - val_loss: 0.7780 - learning_rate: 0.0010
Epoch 3/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.7310 - loss: 0.8015 - val_accuracy: 0.7423 - val_loss: 0.7299 - learning_rate: 0.0010
Epoch 4/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.7547 - loss: 0.7003 - val_accuracy: 0.7470 - val_loss: 0.7101 - learning_rate: 0.0010
Epoch 5/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.7681 - loss: 0.6515 - val_accuracy: 0.7710 - val_loss: 0.6870 - learning_rate: 0.0010
Epoch 6/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.7753 - loss: 0.5997 - val_accuracy: 0.7727 - val_loss: 0.6748 - learning_rate: 0.0010
Epoch 7/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.7890 - loss: 0.5709 - val_accuracy: 0.7793 - val_loss: 0.6708 - learning_rate: 0.0010
Epoch 8/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.7992 - loss: 0.5339 - val_accuracy: 0.7703 - val_loss: 0.6734 - learning_rate: 0.0010
Epoch 9/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.8056 - loss: 0.5120 - val_accuracy: 0.7817 - val_loss: 0.6796 - learning_rate: 0.0010
Epoch 10/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.8133 - loss: 0.4792 - val_accuracy: 0.7897 - val_loss: 0.6761 - learning_rate: 0.0010
Epoch 11/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.8137 - loss: 0.4710 - val_accuracy: 0.7817 - val_loss: 0.6750 - learning_rate: 0.0010
Epoch 12/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.8283 - loss: 0.4526 - val_accuracy: 0.7937 - val_loss: 0.6796 - learning_rate: 0.0010
Epoch 13/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.8237 - loss: 0.4453 - val_accuracy: 0.7877 - val_loss: 0.6989 - learning_rate: 0.0010
Epoch 14/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.8283 - loss: 0.4351 - val_accuracy: 0.7917 - val_loss: 0.7069 - learning_rate: 0.0010
Epoch 15/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.8311 - loss: 0.4195 - val_accuracy: 0.7837 - val_loss: 0.7061 - learning_rate: 0.0010
Epoch 16/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.8427 - loss: 0.3906 - val_accuracy: 0.7943 - val_loss: 0.7226 - learning_rate: 0.0010
Epoch 17/50
367/375 ━━━━━━━━━━━━━━━━━━━━ 0s 3ms/step - accuracy: 0.8360 - loss: 0.4061
Epoch 17: ReduceLROnPlateau reducing learning rate to 0.0005000000237487257.
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.8359 - loss: 0.4062 - val_accuracy: 0.7930 - val_loss: 0.7104 - learning_rate: 0.0010
Epoch 18/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.8522 - loss: 0.3651 - val_accuracy: 0.7953 - val_loss: 0.7308 - learning_rate: 5.0000e-04
Epoch 19/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.8633 - loss: 0.3403 - val_accuracy: 0.7940 - val_loss: 0.7486 - learning_rate: 5.0000e-04
Epoch 20/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.8640 - loss: 0.3291 - val_accuracy: 0.7993 - val_loss: 0.7572 - learning_rate: 5.0000e-04
Epoch 21/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.8710 - loss: 0.3155 - val_accuracy: 0.7977 - val_loss: 0.7934 - learning_rate: 5.0000e-04
Epoch 22/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.8662 - loss: 0.3177 - val_accuracy: 0.7993 - val_loss: 0.7862 - learning_rate: 5.0000e-04
Epoch 22: early stopping
Restoring model weights from the end of the best epoch: 7.
Training completed in 35.10 seconds
94/94 ━━━━━━━━━━━━━━━━━━━━ 1s 5ms/step
BERT-Inspired - Interaction Type Accuracy: 0.7793
BERT-Inspired - Interaction Type F1 Score: 0.7714

Classification Report for BERT-Inspired - Interaction Type:
              precision    recall  f1-score   support

           0       0.68      0.85      0.76       442
           1       0.94      0.80      0.86        20
           2       0.00      0.00      0.00         1
           3       1.00      0.59      0.74        22
           4       0.92      0.61      0.73        18
           5       0.60      0.75      0.67         4
           6       1.00      0.50      0.67         2
           7       0.00      0.00      0.00         1
           8       1.00      1.00      1.00        21
           9       1.00      0.33      0.50         3
          10       1.00      1.00      1.00         2
          11       1.00      0.50      0.67         4
          12       0.79      0.76      0.78        93
          13       1.00      1.00      1.00         1
          19       0.96      0.90      0.93        58
          20       1.00      1.00      1.00       109
          21       0.60      0.56      0.58        16
          22       0.88      0.97      0.93        38
          23       0.75      0.63      0.69       581
          25       1.00      1.00      1.00         4
          26       1.00      0.50      0.67         2
          28       1.00      0.40      0.57        10
          30       0.40      0.12      0.18        68
          31       0.80      0.86      0.83       359
          33       0.00      0.00      0.00         1
          35       0.86      0.75      0.80         8
          36       0.00      0.00      0.00         4
          37       0.74      0.78      0.76       767
          38       0.00      0.00      0.00         3
          39       0.50      1.00      0.67         1
          40       0.00      0.00      0.00         1
          41       0.93      0.94      0.94       322
          42       0.67      0.71      0.69        14

    accuracy                           0.78      3000
   macro avg       0.70      0.60      0.62      3000
weighted avg       0.78      0.78      0.77      3000

Model summary saved to model_summary_bert-inspired_interaction_type_20250602_091736.txt
Keras model saved to saved_models/bert_inspired_interaction_type_20250602_091736.h5
Metadata saved to saved_models/bert_inspired_interaction_type_20250602_091736_metadata.json
Creating BERT-Inspired model...
Current BERT-Inspired parameters:
  embedding_dim: 256
  attention_heads: 3
  attention_dim: 128
  ff_dim_1: 512
  ff_dim_2: 256
  residual_dim: 256
  final_dense_dim: 128
  dropout_embedding: 0.3
  dropout_attention: 0.3
  dropout_ff: 0.4
  dropout_final: 0.5
  learning_rate: 0.001
  batch_size: 32
  epochs: 50
  validation_split: 0.2
  early_stopping_patience: 15
  reduce_lr_patience: 10
  reduce_lr_factor: 0.5
  min_lr: 1e-07

Training BERT-Inspired for Severity Level...
Epoch 1/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 9s 6ms/step - accuracy: 0.6792 - loss: 0.8082 - val_accuracy: 0.8360 - val_loss: 0.3778 - learning_rate: 0.0010
Epoch 2/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.8579 - loss: 0.3654 - val_accuracy: 0.8687 - val_loss: 0.3136 - learning_rate: 0.0010
Epoch 3/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.8865 - loss: 0.2860 - val_accuracy: 0.8737 - val_loss: 0.2890 - learning_rate: 0.0010
Epoch 4/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.9027 - loss: 0.2390 - val_accuracy: 0.8900 - val_loss: 0.2618 - learning_rate: 0.0010
Epoch 5/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.9182 - loss: 0.2095 - val_accuracy: 0.8970 - val_loss: 0.2477 - learning_rate: 0.0010
Epoch 6/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.9230 - loss: 0.1876 - val_accuracy: 0.8983 - val_loss: 0.2478 - learning_rate: 0.0010
Epoch 7/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.9234 - loss: 0.1755 - val_accuracy: 0.9013 - val_loss: 0.2544 - learning_rate: 0.0010
Epoch 8/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.9356 - loss: 0.1579 - val_accuracy: 0.9090 - val_loss: 0.2387 - learning_rate: 0.0010
Epoch 9/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.9406 - loss: 0.1473 - val_accuracy: 0.9057 - val_loss: 0.2498 - learning_rate: 0.0010
Epoch 10/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.9436 - loss: 0.1351 - val_accuracy: 0.9093 - val_loss: 0.2503 - learning_rate: 0.0010
Epoch 11/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.9424 - loss: 0.1300 - val_accuracy: 0.9113 - val_loss: 0.2656 - learning_rate: 0.0010
Epoch 12/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.9476 - loss: 0.1205 - val_accuracy: 0.9127 - val_loss: 0.2621 - learning_rate: 0.0010
Epoch 13/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.9470 - loss: 0.1154 - val_accuracy: 0.9207 - val_loss: 0.2477 - learning_rate: 0.0010
Epoch 14/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.9517 - loss: 0.1042 - val_accuracy: 0.9237 - val_loss: 0.2525 - learning_rate: 0.0010
Epoch 15/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.9523 - loss: 0.1121 - val_accuracy: 0.9250 - val_loss: 0.2597 - learning_rate: 0.0010
Epoch 16/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.9559 - loss: 0.0968 - val_accuracy: 0.9193 - val_loss: 0.2750 - learning_rate: 0.0010
Epoch 17/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.9553 - loss: 0.0962 - val_accuracy: 0.9207 - val_loss: 0.2846 - learning_rate: 0.0010
Epoch 18/50
370/375 ━━━━━━━━━━━━━━━━━━━━ 0s 3ms/step - accuracy: 0.9551 - loss: 0.0993
Epoch 18: ReduceLROnPlateau reducing learning rate to 0.0005000000237487257.
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.9551 - loss: 0.0993 - val_accuracy: 0.9203 - val_loss: 0.2886 - learning_rate: 0.0010
Epoch 19/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.9615 - loss: 0.0851 - val_accuracy: 0.9237 - val_loss: 0.2879 - learning_rate: 5.0000e-04
Epoch 20/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.9640 - loss: 0.0718 - val_accuracy: 0.9277 - val_loss: 0.2940 - learning_rate: 5.0000e-04
Epoch 21/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.9679 - loss: 0.0708 - val_accuracy: 0.9287 - val_loss: 0.2901 - learning_rate: 5.0000e-04
Epoch 22/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.9684 - loss: 0.0647 - val_accuracy: 0.9283 - val_loss: 0.3032 - learning_rate: 5.0000e-04
Epoch 23/50
375/375 ━━━━━━━━━━━━━━━━━━━━ 1s 3ms/step - accuracy: 0.9674 - loss: 0.0682 - val_accuracy: 0.9287 - val_loss: 0.3153 - learning_rate: 5.0000e-04
Epoch 23: early stopping
Restoring model weights from the end of the best epoch: 8.
Training completed in 35.94 seconds
94/94 ━━━━━━━━━━━━━━━━━━━━ 1s 4ms/step
BERT-Inspired - Severity Level Accuracy: 0.9090
BERT-Inspired - Severity Level F1 Score: 0.9093

Classification Report for BERT-Inspired - Severity Level:
              precision    recall  f1-score   support

           0       0.86      0.90      0.87       905
           1       0.95      0.88      0.91       365
           2       0.93      0.92      0.93      1730

    accuracy                           0.91      3000
   macro avg       0.91      0.90      0.90      3000
weighted avg       0.91      0.91      0.91      3000

Model summary saved to model_summary_bert-inspired_severity_level_20250602_091813.txt
Keras model saved to saved_models/bert_inspired_severity_level_20250602_091813.h5
Metadata saved to saved_models/bert_inspired_severity_level_20250602_091813_metadata.json

Step 6: Training History Analysis and Enhanced Visualizations
--------------------------------------------------

Generating comprehensive training history analysis...

Training History Summary:
              Model           Target    Model_Type  Training_Time  Final_Accuracy  Final_F1_Score  Epochs  Best_Epoch  Best_Val_Loss  Best_Val_Accuracy
                SVM Interaction Type   traditional     114.338744        0.752000        0.738826     NaN         NaN            NaN                NaN
                SVM   Severity Level   traditional      65.972679        0.872333        0.872009     NaN         NaN            NaN                NaN
        Naive Bayes Interaction Type   traditional       0.053477        0.350000        0.368370     NaN         NaN            NaN                NaN
        Naive Bayes   Severity Level   traditional       0.042823        0.496000        0.462103     NaN         NaN            NaN                NaN
Logistic Regression Interaction Type   traditional      44.441444        0.734333        0.728412     NaN         NaN            NaN                NaN
Logistic Regression   Severity Level   traditional       2.612037        0.823000        0.820927     NaN         NaN            NaN                NaN
            XGBoost Interaction Type      ensemble      46.513257        0.913667        0.912472     NaN         NaN            NaN                NaN
            XGBoost   Severity Level      ensemble       3.353297        0.977333        0.977289     NaN         NaN            NaN                NaN
           LightGBM Interaction Type      ensemble       3.725106        0.194667        0.154091     NaN         NaN            NaN                NaN
           LightGBM   Severity Level      ensemble       1.002758        0.971000        0.970858     NaN         NaN            NaN                NaN
                CNN Interaction Type deep_learning      77.007850        0.530667        0.497641    50.0        48.0       1.307641           0.531667
                CNN   Severity Level deep_learning      74.984586        0.760667        0.757145    50.0        50.0       0.537749           0.768667
               LSTM Interaction Type deep_learning    8409.155715        0.255667        0.104113    24.0         9.0       2.171566           0.255667
               LSTM   Severity Level deep_learning   13592.025906        0.576667        0.421832    39.0        24.0       0.935294           0.576667
      BERT-Inspired Interaction Type deep_learning      35.102079        0.779333        0.771443    22.0         7.0       0.670761           0.799333
      BERT-Inspired   Severity Level deep_learning      35.936153        0.909000        0.909308    23.0         8.0       0.238725           0.928667

Training summary saved to ddi_analysis_output_20250602_030145/data/training_summary_20250602_030145.csv
Plot saved as ddi_analysis_output_20250602_030145/plots/model_type_comparison_20250602_030145.png
Plot saved as ddi_analysis_output_20250602_030145/plots/model_type_comparison_20250602_030145.pdf

Training histories saved to ddi_analysis_output_20250602_030145/data/training_histories_20250602_030145.json

Generating enhanced visualizations...

Step 7: Comprehensive Model Comparison
--------------------------------------------------

Comprehensive Model Performance Comparison:
                 Model  Interaction Type Accuracy  Interaction Type F1 Score  \
0                  SVM                   0.752000                   0.738826   
1          Naive Bayes                   0.350000                   0.368370   
2  Logistic Regression                   0.734333                   0.728412   
3              XGBoost                   0.913667                   0.912472   
4             LightGBM                   0.194667                   0.154091   
5                  CNN                   0.530667                   0.497641   
6                 LSTM                   0.255667                   0.104113   
7        BERT-Inspired                   0.779333                   0.771443   

   Severity Level Accuracy  Severity Level F1 Score  
0                 0.872333                 0.872009  
1                 0.496000                 0.462103  
2                 0.823000                 0.820927  
3                 0.977333                 0.977289  
4                 0.971000                 0.970858  
5                 0.760667                 0.757145  
6                 0.576667                 0.421832  
7                 0.909000                 0.909308  

Best model for Interaction Type prediction: XGBoost
Best model for Severity Level prediction: XGBoost

Generating comprehensive visualizations...

Top 3 models for Interaction Type: ['XGBoost', 'BERT-Inspired', 'SVM']
Top 3 models for Severity Level: ['XGBoost', 'LightGBM', 'BERT-Inspired']
Plot saved as ddi_analysis_output_20250602_030145/plots/confusion_matrix_xgboost_interaction_type.png
Plot saved as ddi_analysis_output_20250602_030145/plots/confusion_matrix_xgboost_interaction_type.pdf


Detailed Metrics for XGBoost - Interaction Type:
--------------------------------------------------
QTc-prolonging activities | Precision: 0.884 | Recall: 0.912 | F1: 0.898
absorption      | Precision: 1.000 | Recall: 0.850 | F1: 0.919
anticholinergic activities | Precision: 0.000 | Recall: 0.000 | F1: 0.000
anticoagulant activities | Precision: 0.905 | Recall: 0.864 | F1: 0.884
antihypertensive activities | Precision: 1.000 | Recall: 0.667 | F1: 0.800
antiplatelet activities | Precision: 1.000 | Recall: 0.750 | F1: 0.857
arrhythmogenic activities | Precision: 1.000 | Recall: 0.500 | F1: 0.667
atrioventricular blocking (AV block) activities | Precision: 0.000 | Recall: 0.000 | F1: 0.000
bioavailability | Precision: 1.000 | Recall: 1.000 | F1: 1.000
bradycardic activities | Precision: 1.000 | Recall: 0.333 | F1: 0.500
bronchodilatory activities | Precision: 1.000 | Recall: 1.000 | F1: 1.000
cardiotoxic activities | Precision: 1.000 | Recall: 1.000 | F1: 1.000
central nervous system depressant (CNS depressant) activities | Precision: 0.946 | Recall: 0.946 | F1: 0.946
dermatologic adverse activities | Precision: 1.000 | Recall: 1.000 | F1: 1.000
excretion       | Precision: 1.000 | Recall: 0.931 | F1: 0.964
excretion rate  | Precision: 1.000 | Recall: 1.000 | F1: 1.000
excretion rate of higher serum level | Precision: 0.929 | Recall: 0.812 | F1: 0.867
hyperglycemic activities | Precision: 0.974 | Recall: 1.000 | F1: 0.987
hypertensive activities | Precision: 0.878 | Recall: 0.883 | F1: 0.881
hypoglycemic activities | Precision: 0.800 | Recall: 1.000 | F1: 0.889
hypokalemic activities | Precision: 1.000 | Recall: 0.500 | F1: 0.667
hypotensive activities | Precision: 1.000 | Recall: 0.600 | F1: 0.750
immunosuppressive activities | Precision: 0.877 | Recall: 0.735 | F1: 0.800
metabolism      | Precision: 0.942 | Recall: 0.958 | F1: 0.950
myelosuppressive activities | Precision: 0.000 | Recall: 0.000 | F1: 0.000
nephrotoxic activities | Precision: 1.000 | Recall: 1.000 | F1: 1.000
neuroexcitatory activities | Precision: 1.000 | Recall: 0.250 | F1: 0.400
neurotoxic activities | Precision: 0.901 | Recall: 0.914 | F1: 0.907
orthostatic hypotensive activities | Precision: 0.667 | Recall: 0.667 | F1: 0.667
risk of hypersensitivity reaction to | Precision: 1.000 | Recall: 1.000 | F1: 1.000
risk or severity of QTc prolongation | Precision: 1.000 | Recall: 1.000 | F1: 1.000
risk or severity of adverse effects | Precision: 0.954 | Recall: 0.960 | F1: 0.957
risk or severity of bradycardia | Precision: 0.824 | Recall: 1.000 | F1: 0.903
Plot saved as ddi_analysis_output_20250602_030145/plots/confusion_matrix_bert_inspired_interaction_type.png
Plot saved as ddi_analysis_output_20250602_030145/plots/confusion_matrix_bert_inspired_interaction_type.pdf


Detailed Metrics for BERT-Inspired - Interaction Type:
--------------------------------------------------
QTc-prolonging activities | Precision: 0.685 | Recall: 0.851 | F1: 0.759
absorption      | Precision: 0.941 | Recall: 0.800 | F1: 0.865
anticholinergic activities | Precision: 0.000 | Recall: 0.000 | F1: 0.000
anticoagulant activities | Precision: 1.000 | Recall: 0.591 | F1: 0.743
antihypertensive activities | Precision: 0.917 | Recall: 0.611 | F1: 0.733
antiplatelet activities | Precision: 0.600 | Recall: 0.750 | F1: 0.667
arrhythmogenic activities | Precision: 1.000 | Recall: 0.500 | F1: 0.667
atrioventricular blocking (AV block) activities | Precision: 0.000 | Recall: 0.000 | F1: 0.000
bioavailability | Precision: 1.000 | Recall: 1.000 | F1: 1.000
bradycardic activities | Precision: 1.000 | Recall: 0.333 | F1: 0.500
bronchodilatory activities | Precision: 1.000 | Recall: 1.000 | F1: 1.000
cardiotoxic activities | Precision: 1.000 | Recall: 0.500 | F1: 0.667
central nervous system depressant (CNS depressant) activities | Precision: 0.789 | Recall: 0.763 | F1: 0.776
dermatologic adverse activities | Precision: 1.000 | Recall: 1.000 | F1: 1.000
excretion       | Precision: 0.963 | Recall: 0.897 | F1: 0.929
excretion rate  | Precision: 1.000 | Recall: 1.000 | F1: 1.000
excretion rate of higher serum level | Precision: 0.600 | Recall: 0.562 | F1: 0.581
hyperglycemic activities | Precision: 0.881 | Recall: 0.974 | F1: 0.925
hypertensive activities | Precision: 0.752 | Recall: 0.632 | F1: 0.687
hypoglycemic activities | Precision: 1.000 | Recall: 1.000 | F1: 1.000
hypokalemic activities | Precision: 1.000 | Recall: 0.500 | F1: 0.667
hypotensive activities | Precision: 1.000 | Recall: 0.400 | F1: 0.571
immunosuppressive activities | Precision: 0.400 | Recall: 0.118 | F1: 0.182
metabolism      | Precision: 0.798 | Recall: 0.858 | F1: 0.827
myelosuppressive activities | Precision: 0.000 | Recall: 0.000 | F1: 0.000
nephrotoxic activities | Precision: 0.857 | Recall: 0.750 | F1: 0.800
neuroexcitatory activities | Precision: 0.000 | Recall: 0.000 | F1: 0.000
neurotoxic activities | Precision: 0.739 | Recall: 0.782 | F1: 0.760
orthostatic hypotensive activities | Precision: 0.000 | Recall: 0.000 | F1: 0.000
risk of hypersensitivity reaction to | Precision: 0.500 | Recall: 1.000 | F1: 0.667
risk or severity of QTc prolongation | Precision: 0.000 | Recall: 0.000 | F1: 0.000
risk or severity of adverse effects | Precision: 0.930 | Recall: 0.944 | F1: 0.937
risk or severity of bradycardia | Precision: 0.667 | Recall: 0.714 | F1: 0.690
Plot saved as ddi_analysis_output_20250602_030145/plots/confusion_matrix_svm_interaction_type.png
Plot saved as ddi_analysis_output_20250602_030145/plots/confusion_matrix_svm_interaction_type.pdf


Detailed Metrics for SVM - Interaction Type:
--------------------------------------------------
QTc-prolonging activities | Precision: 0.662 | Recall: 0.810 | F1: 0.728
absorption      | Precision: 1.000 | Recall: 0.750 | F1: 0.857
anticholinergic activities | Precision: 0.000 | Recall: 0.000 | F1: 0.000
anticoagulant activities | Precision: 0.783 | Recall: 0.818 | F1: 0.800
antihypertensive activities | Precision: 0.750 | Recall: 0.500 | F1: 0.600
antiplatelet activities | Precision: 1.000 | Recall: 0.250 | F1: 0.400
arrhythmogenic activities | Precision: 0.000 | Recall: 0.000 | F1: 0.000
atrioventricular blocking (AV block) activities | Precision: 0.000 | Recall: 0.000 | F1: 0.000
bioavailability | Precision: 1.000 | Recall: 1.000 | F1: 1.000
bradycardic activities | Precision: 1.000 | Recall: 0.333 | F1: 0.500
bronchodilatory activities | Precision: 0.000 | Recall: 0.000 | F1: 0.000
cardiotoxic activities | Precision: 1.000 | Recall: 0.500 | F1: 0.667
central nervous system depressant (CNS depressant) activities | Precision: 0.795 | Recall: 0.667 | F1: 0.725
dermatologic adverse activities | Precision: 0.000 | Recall: 0.000 | F1: 0.000
excretion       | Precision: 0.979 | Recall: 0.810 | F1: 0.887
excretion rate  | Precision: 1.000 | Recall: 0.991 | F1: 0.995
excretion rate of higher serum level | Precision: 0.667 | Recall: 0.625 | F1: 0.645
hyperglycemic activities | Precision: 0.800 | Recall: 0.947 | F1: 0.867
hypertensive activities | Precision: 0.732 | Recall: 0.570 | F1: 0.641
hypoglycemic activities | Precision: 1.000 | Recall: 0.750 | F1: 0.857
hypokalemic activities | Precision: 1.000 | Recall: 0.500 | F1: 0.667
hypotensive activities | Precision: 1.000 | Recall: 0.400 | F1: 0.571
immunosuppressive activities | Precision: 1.000 | Recall: 0.029 | F1: 0.057
metabolism      | Precision: 0.755 | Recall: 0.886 | F1: 0.815
myelosuppressive activities | Precision: 0.000 | Recall: 0.000 | F1: 0.000
nephrotoxic activities | Precision: 0.625 | Recall: 0.625 | F1: 0.625
neuroexcitatory activities | Precision: 0.000 | Recall: 0.000 | F1: 0.000
neurotoxic activities | Precision: 0.693 | Recall: 0.799 | F1: 0.742
orthostatic hypotensive activities | Precision: 0.000 | Recall: 0.000 | F1: 0.000
risk of hypersensitivity reaction to | Precision: 0.333 | Recall: 1.000 | F1: 0.500
risk or severity of QTc prolongation | Precision: 0.000 | Recall: 0.000 | F1: 0.000
risk or severity of adverse effects | Precision: 0.932 | Recall: 0.894 | F1: 0.913
risk or severity of bradycardia | Precision: 1.000 | Recall: 0.143 | F1: 0.250
Plot saved as ddi_analysis_output_20250602_030145/plots/confusion_matrix_xgboost_severity_level.png
Plot saved as ddi_analysis_output_20250602_030145/plots/confusion_matrix_xgboost_severity_level.pdf


Detailed Metrics for XGBoost - Severity Level:
--------------------------------------------------
Major           | Precision: 0.974 | Recall: 0.968 | F1: 0.971
Minor           | Precision: 0.977 | Recall: 0.951 | F1: 0.964
Moderate        | Precision: 0.979 | Recall: 0.988 | F1: 0.983
Plot saved as ddi_analysis_output_20250602_030145/plots/confusion_matrix_lightgbm_severity_level.png
Plot saved as ddi_analysis_output_20250602_030145/plots/confusion_matrix_lightgbm_severity_level.pdf


Detailed Metrics for LightGBM - Severity Level:
--------------------------------------------------
Major           | Precision: 0.974 | Recall: 0.950 | F1: 0.962
Minor           | Precision: 0.983 | Recall: 0.929 | F1: 0.955
Moderate        | Precision: 0.967 | Recall: 0.991 | F1: 0.979
Plot saved as ddi_analysis_output_20250602_030145/plots/confusion_matrix_bert_inspired_severity_level.png
Plot saved as ddi_analysis_output_20250602_030145/plots/confusion_matrix_bert_inspired_severity_level.pdf


Detailed Metrics for BERT-Inspired - Severity Level:
--------------------------------------------------
Major           | Precision: 0.855 | Recall: 0.895 | F1: 0.875
Minor           | Precision: 0.947 | Recall: 0.882 | F1: 0.913
Moderate        | Precision: 0.931 | Recall: 0.922 | F1: 0.927
Plot saved as ddi_analysis_output_20250602_030145/plots/training_history_interaction_type.png
Plot saved as ddi_analysis_output_20250602_030145/plots/training_history_interaction_type.pdf

Plot saved as ddi_analysis_output_20250602_030145/plots/training_history_severity_level.png
Plot saved as ddi_analysis_output_20250602_030145/plots/training_history_severity_level.pdf

Plot saved as ddi_analysis_output_20250602_030145/plots/comprehensive_model_comparison.png
Plot saved as ddi_analysis_output_20250602_030145/plots/comprehensive_model_comparison.pdf


Step 8: Detailed Algorithm Descriptions and Analysis
--------------------------------------------------

================================================================================
COMPREHENSIVE ALGORITHM DESCRIPTIONS AND ANALYSIS
================================================================================

1. SUPPORT VECTOR MACHINE (SVM)
----------------------------------------

    Description:
    SVM is a powerful supervised learning algorithm that finds the optimal hyperplane
    to separate different classes in high-dimensional space. It uses kernel functions
    to transform data into higher dimensions where linear separation becomes possible.

    Key Features:
    - Uses RBF (Radial Basis Function) kernel for non-linear classification
    - Effective in high-dimensional spaces
    - Memory efficient as it uses support vectors
    - Robust to overfitting, especially in high-dimensional space

    Advantages:
    - Excellent performance on complex, non-linear problems
    - Works well with limited data
    - Robust to outliers

    Disadvantages:
    - Computationally expensive for large datasets
    - Sensitive to feature scaling
    - No probabilistic output (though we enabled probability=True)
    

2. NAIVE BAYES
----------------------------------------

    Description:
    Naive Bayes is a probabilistic classifier based on Bayes' theorem with the
    "naive" assumption of conditional independence between features. Despite this
    strong assumption, it often performs surprisingly well in practice.

    Key Features:
    - Assumes Gaussian distribution of features (GaussianNB)
    - Fast training and prediction
    - Requires small training datasets
    - Handles multi-class classification naturally

    Advantages:
    - Fast and simple
    - Works well with small datasets
    - Not sensitive to irrelevant features
    - Good baseline model

    Disadvantages:
    - Strong independence assumption rarely holds in reality
    - Can be outperformed by more sophisticated methods
    - Sensitive to skewed data
    

3. LOGISTIC REGRESSION
----------------------------------------

    Description:
    Logistic Regression uses the logistic function to model the probability of
    class membership. It's a linear classifier that can handle multi-class
    problems using multinomial approach.

    Key Features:
    - Uses multinomial approach for multi-class classification
    - Provides probabilistic outputs
    - Linear decision boundaries
    - Regularization to prevent overfitting

    Advantages:
    - Interpretable coefficients
    - Fast training and prediction
    - No tuning of hyperparameters required
    - Probabilistic output

    Disadvantages:
    - Assumes linear relationship between features and log-odds
    - Sensitive to outliers
    - Requires large sample sizes for stable results
    

4. XGBOOST (EXTREME GRADIENT BOOSTING)
----------------------------------------

    Description:
    XGBoost is an optimized gradient boosting framework that builds models
    sequentially, where each new model corrects errors made by previous models.
    It's known for its performance in machine learning competitions.

    Key Features:
    - Gradient boosting with advanced regularization
    - Handles missing values automatically
    - Built-in cross-validation
    - Feature importance calculation

    Advantages:
    - Excellent predictive performance
    - Handles mixed data types well
    - Built-in regularization prevents overfitting
    - Provides feature importance

    Disadvantages:
    - Can be prone to overfitting with small datasets
    - Requires hyperparameter tuning
    - Less interpretable than linear models
    

5. LIGHTGBM (LIGHT GRADIENT BOOSTING MACHINE)
----------------------------------------

    Description:
    LightGBM is a gradient boosting framework that uses tree-based learning
    algorithms. It's designed to be distributed and efficient with faster
    training speed and higher efficiency.

    Key Features:
    - Leaf-wise tree growth (vs level-wise in traditional methods)
    - Faster training speed
    - Lower memory usage
    - Better accuracy than many other boosting tools

    Advantages:
    - Very fast training
    - High accuracy
    - Memory efficient
    - Handles large datasets well

    Disadvantages:
    - Can overfit with small datasets
    - Sensitive to hyperparameters
    - May require more careful tuning
    

6. CONVOLUTIONAL NEURAL NETWORK (CNN)
----------------------------------------

    Description:
    CNN adapted for tabular data uses 1D convolutions to detect local patterns
    in feature sequences. Originally designed for image processing, we've adapted
    it for drug interaction prediction by treating features as a 1D sequence.

    Key Features:
    - 1D convolutions for pattern detection
    - Max pooling for dimensionality reduction
    - Multiple convolutional layers with different filter sizes
    - Dropout for regularization

    Advantages:
    - Can detect local patterns in feature space
    - Translation invariant (patterns detected regardless of position)
    - Hierarchical feature learning
    - Good for sequential or spatial data

    Disadvantages:
    - Requires large amounts of data
    - Computationally expensive
    - Many hyperparameters to tune
    - May not be optimal for tabular data
    

7. LONG SHORT-TERM MEMORY (LSTM)
----------------------------------------

    Description:
    LSTM is a type of recurrent neural network capable of learning long-term
    dependencies. For drug interactions, we use it to model sequential
    relationships between drug features.

    Key Features:
    - Memory cells to store information
    - Forget, input, and output gates
    - Handles vanishing gradient problem
    - Bidirectional processing capability

    Advantages:
    - Excellent for sequential data
    - Can learn long-term dependencies
    - Handles variable-length sequences
    - Good for temporal patterns

    Disadvantages:
    - Computationally expensive
    - Requires large datasets
    - Many parameters to train
    - May be overkill for non-sequential tabular data
    

8. BERT-INSPIRED TRANSFORMER MODEL
----------------------------------------

    Description:
    A transformer-inspired architecture adapted for tabular data that simulates
    the attention mechanism of BERT. It uses multiple attention heads to focus
    on different aspects of the input features.

    Key Features:
    - Multi-head attention simulation
    - Residual connections
    - Feed-forward networks
    - Dense layers instead of true attention (adapted for tabular data)

    Advantages:
    - Can model complex feature interactions
    - Attention mechanism highlights important features
    - Parallel processing capability
    - State-of-the-art architecture adaptation

    Disadvantages:
    - Very computationally expensive
    - Requires large amounts of data
    - Many parameters to optimize
    - Complex architecture may be unnecessary for simple problems
    

Step 9: Feature Importance Analysis
--------------------------------------------------

Top 10 important features for Interaction Type prediction:
                                     Feature  Importance
187     Pharmacodynamic_Class_B_Antiandrogen    0.088870
179    Pharmacodynamic_Class_B_Amylin Analog    0.074510
261   Pharmacodynamic_Class_B_PARP Inhibitor    0.068448
222  Pharmacodynamic_Class_B_Chelating Agent    0.050362
75     Pharmacodynamic_Class_A_Amylin Analog    0.043841
184          Pharmacodynamic_Class_B_Antacid    0.033418
90      Pharmacodynamic_Class_A_Antidiabetic    0.028967
111   Pharmacodynamic_Class_A_Bronchodilator    0.028162
195     Pharmacodynamic_Class_B_Antidiabetic    0.022717
236     Pharmacodynamic_Class_B_GnRH Agonist    0.022240

Top 10 important features for Severity Level prediction:
                                           Feature  Importance
184                Pharmacodynamic_Class_B_Antacid    0.073893
216         Pharmacodynamic_Class_B_Bronchodilator    0.039890
141       Pharmacodynamic_Class_A_Local Anesthetic    0.029697
174                Pharmacodynamic_Class_A_Vitamin    0.028326
162               Pharmacodynamic_Class_A_Retinoid    0.026202
90            Pharmacodynamic_Class_A_Antidiabetic    0.023986
195           Pharmacodynamic_Class_B_Antidiabetic    0.021812
259      Pharmacodynamic_Class_B_Opioid Antagonist    0.021174
200          Pharmacodynamic_Class_B_Antihistamine    0.019942
161  Pharmacodynamic_Class_A_Proton Pump Inhibitor    0.019105
Plot saved as ddi_analysis_output_20250602_030145/plots/feature_importance_analysis.png
Plot saved as ddi_analysis_output_20250602_030145/plots/feature_importance_analysis.pdf


Step 10: Comprehensive Model Comparison and Analysis
--------------------------------------------------

================================================================================
COMPREHENSIVE MODEL PERFORMANCE ANALYSIS
================================================================================

RANKED MODEL PERFORMANCE (by Average Performance):
------------------------------------------------------------
XGBoost         | Avg: 0.9452 | Type: 0.9125 | Level: 0.9773
BERT-Inspired   | Avg: 0.8423 | Type: 0.7714 | Level: 0.9093
SVM             | Avg: 0.8088 | Type: 0.7388 | Level: 0.8720
Logistic Regression | Avg: 0.7767 | Type: 0.7284 | Level: 0.8209
CNN             | Avg: 0.6365 | Type: 0.4976 | Level: 0.7571
LightGBM        | Avg: 0.5727 | Type: 0.1541 | Level: 0.9709
Naive Bayes     | Avg: 0.4191 | Type: 0.3684 | Level: 0.4621
LSTM            | Avg: 0.3396 | Type: 0.1041 | Level: 0.4218

================================================================================
ALGORITHM CATEGORY ANALYSIS
================================================================================

Traditional ML Average Performance: 0.6682
Ensemble Methods Average Performance: 0.7589
Deep Learning Average Performance: 0.6061

Best Performing Category: Ensemble Methods

================================================================================
DETAILED RECOMMENDATIONS
================================================================================

1. BEST OVERALL MODELS:
   4. XGBoost (Avg Performance: 0.9452)
   8. BERT-Inspired (Avg Performance: 0.8423)
   1. SVM (Avg Performance: 0.8088)

2. TASK-SPECIFIC RECOMMENDATIONS:
   - For Interaction Type Prediction: XGBoost
     (F1 Score: 0.9125)
   - For Severity Level Prediction: XGBoost
     (F1 Score: 0.9773)

3. COMPUTATIONAL EFFICIENCY CONSIDERATIONS:
   - Fastest: Naive Bayes, Logistic Regression
   - Balanced Speed/Performance: XGBoost, LightGBM
   - Highest Performance (but slower): Deep Learning models

4. DATA SIZE RECOMMENDATIONS:
   - Small datasets (<1000 samples): SVM, Naive Bayes
   - Medium datasets (1000-10000): XGBoost, LightGBM
   - Large datasets (>10000): Deep Learning models

5. INTERPRETABILITY RANKING:
   1. Logistic Regression (highest interpretability)
   2. Naive Bayes
   3. XGBoost/LightGBM (feature importance)
   4. SVM
   5. Deep Learning models (lowest interpretability)

Step 11: Novel Drug Prediction Framework
--------------------------------------------------
Novel drug prediction framework implemented.
This framework can be used to predict interactions for new drug pairs based on their features.

================================================================================
PARAMETER TUNING AND RESULTS MANAGEMENT
================================================================================

Saving current parameters...
Parameters saved to model_parameters_20250602_091854.json
Saving experiment results...
Results saved to experiment_results_20250602_030145.json
Creating results summary...

Experiment Results Summary:
              Model           Target  Accuracy  F1_Score  Training_Time
                SVM Interaction Type  0.752000  0.738826     114.338744
                SVM   Severity Level  0.872333  0.872009      65.972679
        Naive Bayes Interaction Type  0.350000  0.368370       0.053477
        Naive Bayes   Severity Level  0.496000  0.462103       0.042823
Logistic Regression Interaction Type  0.734333  0.728412      44.441444
Logistic Regression   Severity Level  0.823000  0.820927       2.612037
            XGBoost Interaction Type  0.913667  0.912472      46.513257
            XGBoost   Severity Level  0.977333  0.977289       3.353297
           LightGBM Interaction Type  0.194667  0.154091       3.725106
           LightGBM   Severity Level  0.971000  0.970858       1.002758
                CNN Interaction Type  0.530667  0.497641      77.007850
                CNN   Severity Level  0.760667  0.757145      74.984586
               LSTM Interaction Type  0.255667  0.104113    8409.155715
               LSTM   Severity Level  0.576667  0.421832   13592.025906
      BERT-Inspired Interaction Type  0.779333  0.771443      35.102079
      BERT-Inspired   Severity Level  0.909000  0.909308      35.936153

Results summary saved to ddi_analysis_output_20250602_030145/data/experiment_results_summary_20250602_030145.csv

Best Results for Each Model and Target:
SVM_Interaction Type: Accuracy=0.7520, F1=0.7388
SVM_Severity Level: Accuracy=0.8723, F1=0.8720
Naive Bayes_Interaction Type: Accuracy=0.3500, F1=0.3684
Naive Bayes_Severity Level: Accuracy=0.4960, F1=0.4621
Logistic Regression_Interaction Type: Accuracy=0.7343, F1=0.7284
Logistic Regression_Severity Level: Accuracy=0.8230, F1=0.8209
XGBoost_Interaction Type: Accuracy=0.9137, F1=0.9125
XGBoost_Severity Level: Accuracy=0.9773, F1=0.9773
LightGBM_Interaction Type: Accuracy=0.1947, F1=0.1541
LightGBM_Severity Level: Accuracy=0.9710, F1=0.9709
CNN_Interaction Type: Accuracy=0.5307, F1=0.4976
CNN_Severity Level: Accuracy=0.7607, F1=0.7571
LSTM_Interaction Type: Accuracy=0.2557, F1=0.1041
LSTM_Severity Level: Accuracy=0.5767, F1=0.4218
BERT-Inspired_Interaction Type: Accuracy=0.7793, F1=0.7714
BERT-Inspired_Severity Level: Accuracy=0.9090, F1=0.9093

================================================================================
PARAMETER MODIFICATION GUIDE
================================================================================

To modify parameters for better results, you can:

1. EDIT PARAMETERS DIRECTLY IN CODE:
   - Modify the ModelParameters class __init__ method
   - Change values in model_params.svm_params, model_params.xgb_params, etc.

2. LOAD PARAMETERS FROM FILE:
   - Edit the saved JSON file: model_parameters_20250602_091854.json
   - Load with: model_params.load_parameters('filename.json')

3. MODIFY SPECIFIC ALGORITHM PARAMETERS:

   SVM Parameters to try:
   - C: [0.1, 1.0, 10.0, 100.0] (regularization strength)
   - kernel: ['linear', 'rbf', 'poly'] (kernel type)
   - gamma: ['scale', 'auto', 0.001, 0.01, 0.1, 1.0] (kernel coefficient)

   XGBoost Parameters to try:
   - learning_rate: [0.01, 0.1, 0.2, 0.3] (step size shrinkage)
   - max_depth: [3, 6, 10, 15] (maximum tree depth)
   - n_estimators: [50, 100, 200, 500] (number of trees)
   - subsample: [0.8, 0.9, 1.0] (subsample ratio)

   LightGBM Parameters to try:
   - learning_rate: [0.01, 0.05, 0.1, 0.2] (boosting learning rate)
   - num_leaves: [15, 31, 63, 127] (max tree leaves)
   - min_child_samples: [10, 20, 30, 50] (min data in leaf)

   CNN Parameters to try:
   - filters_1: [32, 64, 128] (number of filters in first layer)
   - dropout_conv: [0.2, 0.3, 0.4, 0.5] (dropout rate)
   - learning_rate: [0.0001, 0.001, 0.01] (optimizer learning rate)
   - batch_size: [16, 32, 64] (training batch size)

   LSTM Parameters to try:
   - lstm_units_1: [64, 128, 256] (LSTM units in first layer)
   - dropout: [0.2, 0.3, 0.4, 0.5] (dropout rate)
   - learning_rate: [0.0001, 0.001, 0.01] (optimizer learning rate)

4. EXAMPLE PARAMETER MODIFICATION:

   # Modify SVM parameters for better performance
   model_params.svm_params['C'] = 10.0
   model_params.svm_params['gamma'] = 0.01

   # Modify XGBoost parameters
   model_params.xgb_params['learning_rate'] = 0.1
   model_params.xgb_params['max_depth'] = 10
   model_params.xgb_params['n_estimators'] = 200

   # Then re-run the training sections

5. AUTOMATED PARAMETER TUNING:
   - Use GridSearchCV or RandomizedSearchCV for systematic tuning
   - Implement Bayesian optimization for efficient parameter search
   - Use cross-validation for robust parameter evaluation



Step 12: Multi-Drug Interaction Framework
--------------------------------------------------
Multi-drug interaction framework implemented.
This framework can predict interactions among multiple drugs by analyzing pairwise interactions.

Step 13: Final Summary and Conclusion
--------------------------------------------------

================================================================================
DRUG-DRUG INTERACTION PREDICTION MODEL - FINAL SUMMARY
================================================================================

Best Overall Model for Interaction Type prediction: XGBoost
Best Overall Model for Severity Level prediction: XGBoost

Key Achievements:
✓ Implemented 8 different machine learning algorithms
✓ Comprehensive comparison including traditional ML, ensemble, and deep learning
✓ Generated detailed visualizations and confusion matrices
✓ Feature importance analysis
✓ Novel drug prediction framework
✓ Multi-drug interaction prediction capability

Model Capabilities:
1. Predict interaction types between known drugs
2. Predict severity levels of interactions
3. Handle interactions with novel drugs
4. Analyze interactions among multiple drugs
5. Provide feature importance insights

Comprehensive summary saved to ddi_analysis_output_20250602_030145/reports/ddi_comprehensive_summary_20250602_091854.txt

================================================================================
FINALIZING OUTPUT FILES AND CREATING ZIP ARCHIVE
================================================================================
File summary saved to ddi_analysis_output_20250602_030145/reports/file_summary.txt

Creating ZIP archive: ddi_analysis_complete_20250602_030145.zip
==================================================
Added to ZIP: ddi_analysis_output_20250602_030145/reports/ddi_comprehensive_summary_20250602_091854.txt
Added to ZIP: ddi_analysis_output_20250602_030145/reports/file_summary.txt
Added to ZIP: ddi_analysis_output_20250602_030145/data/training_histories_20250602_030145.json
Added to ZIP: ddi_analysis_output_20250602_030145/data/experiment_results_summary_20250602_030145.csv
Added to ZIP: ddi_analysis_output_20250602_030145/data/training_summary_20250602_030145.csv
Added to ZIP: ddi_analysis_output_20250602_030145/plots/comprehensive_model_comparison.png
Added to ZIP: ddi_analysis_output_20250602_030145/plots/confusion_matrix_xgboost_interaction_type.png
Added to ZIP: ddi_analysis_output_20250602_030145/plots/training_history_severity_level.pdf
Added to ZIP: ddi_analysis_output_20250602_030145/plots/model_type_comparison_20250602_030145.pdf
Added to ZIP: ddi_analysis_output_20250602_030145/plots/confusion_matrix_bert_inspired_interaction_type.png
Added to ZIP: ddi_analysis_output_20250602_030145/plots/confusion_matrix_lightgbm_severity_level.png
Added to ZIP: ddi_analysis_output_20250602_030145/plots/confusion_matrix_xgboost_severity_level.png
Added to ZIP: ddi_analysis_output_20250602_030145/plots/feature_importance_analysis.png
Added to ZIP: ddi_analysis_output_20250602_030145/plots/confusion_matrix_xgboost_severity_level.pdf
Added to ZIP: ddi_analysis_output_20250602_030145/plots/confusion_matrix_xgboost_interaction_type.pdf
Added to ZIP: ddi_analysis_output_20250602_030145/plots/training_history_interaction_type.png
Added to ZIP: ddi_analysis_output_20250602_030145/plots/confusion_matrix_svm_interaction_type.png
Added to ZIP: ddi_analysis_output_20250602_030145/plots/confusion_matrix_bert_inspired_severity_level.pdf
Added to ZIP: ddi_analysis_output_20250602_030145/plots/comprehensive_model_comparison.pdf
Added to ZIP: ddi_analysis_output_20250602_030145/plots/confusion_matrix_svm_interaction_type.pdf
Added to ZIP: ddi_analysis_output_20250602_030145/plots/confusion_matrix_lightgbm_severity_level.pdf
Added to ZIP: ddi_analysis_output_20250602_030145/plots/feature_importance_analysis.pdf
Added to ZIP: ddi_analysis_output_20250602_030145/plots/training_history_interaction_type.pdf
Added to ZIP: ddi_analysis_output_20250602_030145/plots/confusion_matrix_bert_inspired_interaction_type.pdf
Added to ZIP: ddi_analysis_output_20250602_030145/plots/confusion_matrix_bert_inspired_severity_level.png
Added to ZIP: ddi_analysis_output_20250602_030145/plots/model_type_comparison_20250602_030145.png
Added to ZIP: ddi_analysis_output_20250602_030145/plots/training_history_severity_level.png

ZIP archive created successfully!
Archive name: ddi_analysis_complete_20250602_030145.zip
Archive size: 5.08 MB
Total files included: 27

================================================================================
ALL ANALYSIS COMPLETE - FILES READY FOR DOWNLOAD!
================================================================================

📁 All output files have been organized in: ddi_analysis_output_20250602_030145
📦 Complete ZIP archive created: ddi_analysis_complete_20250602_030145.zip
🎯 Experiment ID: 20250602_030145

📊 Generated Files Summary:
   • Plots: 22 files (PNG + PDF formats)
   • Data: 3 files (CSV, JSON)
   • Reports: 2 files (TXT summaries)
   • Models: 0 files (Saved models)

🔍 Key Features:
   ✓ High-quality plots (300 DPI) in multiple formats
   ✓ Enhanced confusion matrices with improved readability
   ✓ Organized directory structure
   ✓ Complete ZIP archive for easy distribution
   ✓ Comprehensive analysis reports

================================================================================
READY FOR KAGGLE EXECUTION AND DISTRIBUTION!
================================================================================