=== XGBoost Model Summary for Severity Level ===
Date and Time: 2025-06-02 03:06:38

=== Model Parameters ===
max_depth: 6
learning_rate: 0.3
n_estimators: 400
verbosity: 0
objective: multi:softprob
booster: gbtree
tree_method: auto
n_jobs: -1
gamma: 0
min_child_weight: 1
max_delta_step: 0
subsample: 1
colsample_bytree: 1
colsample_bylevel: 1
colsample_bynode: 1
reg_alpha: 0
reg_lambda: 1
scale_pos_weight: 1
base_score: 0.5
random_state: 42
num_parallel_tree: 1
importance_type: gain
gpu_id: -1
validate_parameters: True

=== Performance Metrics ===
Accuracy: 0.9773
F1 Score: 0.9773
Training Time: 3.35 seconds

=== Classification Report ===
              precision    recall  f1-score   support

           0       0.97      0.97      0.97       905
           1       0.98      0.95      0.96       365
           2       0.98      0.99      0.98      1730

    accuracy                           0.98      3000
   macro avg       0.98      0.97      0.97      3000
weighted avg       0.98      0.98      0.98      3000
