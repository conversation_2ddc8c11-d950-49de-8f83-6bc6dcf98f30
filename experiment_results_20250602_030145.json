[{"experiment_id": "20250602_030145", "timestamp": "2025-06-02T03:03:50.265898", "model_name": "SVM", "target_type": "Interaction Type", "parameters": {"C": 1.0, "kernel": "rbf", "degree": 3, "gamma": "scale", "coef0": 0.0, "shrinking": true, "probability": true, "tol": 0.001, "cache_size": 200, "class_weight": null, "verbose": false, "max_iter": -1, "decision_function_shape": "ovr", "break_ties": false, "random_state": 42}, "metrics": {"accuracy": 0.752, "f1_score": 0.7388263112501393}, "training_time": 114.33874416351318, "model_path": "saved_models/svm_interaction_type_20250602_030350.pkl"}, {"experiment_id": "20250602_030145", "timestamp": "2025-06-02T03:05:01.147528", "model_name": "SVM", "target_type": "Severity Level", "parameters": {"C": 1.0, "kernel": "rbf", "degree": 3, "gamma": "scale", "coef0": 0.0, "shrinking": true, "probability": true, "tol": 0.001, "cache_size": 200, "class_weight": null, "verbose": false, "max_iter": -1, "decision_function_shape": "ovr", "break_ties": false, "random_state": 42}, "metrics": {"accuracy": 0.8723333333333333, "f1_score": 0.8720086217312757}, "training_time": 65.97267937660217, "model_path": "saved_models/svm_severity_level_20250602_030501.pkl"}, {"experiment_id": "20250602_030145", "timestamp": "2025-06-02T03:05:01.346899", "model_name": "<PERSON><PERSON>", "target_type": "Interaction Type", "parameters": {"priors": null, "var_smoothing": 1e-09}, "metrics": {"accuracy": 0.35, "f1_score": 0.36836956495509676}, "training_time": 0.05347704887390137, "model_path": "saved_models/naive_bayes_interaction_type_20250602_030501.pkl"}, {"experiment_id": "20250602_030145", "timestamp": "2025-06-02T03:05:01.410595", "model_name": "<PERSON><PERSON>", "target_type": "Severity Level", "parameters": {"priors": null, "var_smoothing": 1e-09}, "metrics": {"accuracy": 0.496, "f1_score": 0.4621029854459957}, "training_time": 0.042822837829589844, "model_path": "saved_models/naive_bayes_severity_level_20250602_030501.pkl"}, {"experiment_id": "20250602_030145", "timestamp": "2025-06-02T03:05:45.887556", "model_name": "Logistic Regression", "target_type": "Interaction Type", "parameters": {"penalty": "l2", "dual": false, "tol": 0.0001, "C": 1.0, "fit_intercept": true, "intercept_scaling": 1, "class_weight": null, "random_state": 42, "solver": "lbfgs", "max_iter": 1000, "multi_class": "multinomial", "verbose": 0, "warm_start": false, "n_jobs": null, "l1_ratio": null}, "metrics": {"accuracy": 0.7343333333333333, "f1_score": 0.7284121525623938}, "training_time": 44.441444396972656, "model_path": "saved_models/logistic_regression_interaction_type_20250602_030545.pkl"}, {"experiment_id": "20250602_030145", "timestamp": "2025-06-02T03:05:48.531407", "model_name": "Logistic Regression", "target_type": "Severity Level", "parameters": {"penalty": "l2", "dual": false, "tol": 0.0001, "C": 1.0, "fit_intercept": true, "intercept_scaling": 1, "class_weight": null, "random_state": 42, "solver": "lbfgs", "max_iter": 1000, "multi_class": "multinomial", "verbose": 0, "warm_start": false, "n_jobs": null, "l1_ratio": null}, "metrics": {"accuracy": 0.823, "f1_score": 0.8209268622332847}, "training_time": 2.612036943435669, "model_path": "saved_models/logistic_regression_severity_level_20250602_030548.pkl"}, {"experiment_id": "20250602_030145", "timestamp": "2025-06-02T03:06:35.567432", "model_name": "XGBoost", "target_type": "Interaction Type", "parameters": {"max_depth": 6, "learning_rate": 0.3, "n_estimators": 400, "verbosity": 0, "objective": "multi:softprob", "booster": "gbtree", "tree_method": "auto", "n_jobs": -1, "gamma": 0, "min_child_weight": 1, "max_delta_step": 0, "subsample": 1, "colsample_bytree": 1, "colsample_bylevel": 1, "colsample_bynode": 1, "reg_alpha": 0, "reg_lambda": 1, "scale_pos_weight": 1, "base_score": 0.5, "random_state": 42, "num_parallel_tree": 1, "importance_type": "gain", "gpu_id": -1, "validate_parameters": true}, "metrics": {"accuracy": 0.9136666666666666, "f1_score": 0.9124718736588521}, "training_time": 46.51325702667236, "model_path": "saved_models/xgboost_interaction_type_20250602_030635.pkl"}, {"experiment_id": "20250602_030145", "timestamp": "2025-06-02T03:06:38.991274", "model_name": "XGBoost", "target_type": "Severity Level", "parameters": {"max_depth": 6, "learning_rate": 0.3, "n_estimators": 400, "verbosity": 0, "objective": "multi:softprob", "booster": "gbtree", "tree_method": "auto", "n_jobs": -1, "gamma": 0, "min_child_weight": 1, "max_delta_step": 0, "subsample": 1, "colsample_bytree": 1, "colsample_bylevel": 1, "colsample_bynode": 1, "reg_alpha": 0, "reg_lambda": 1, "scale_pos_weight": 1, "base_score": 0.5, "random_state": 42, "num_parallel_tree": 1, "importance_type": "gain", "gpu_id": -1, "validate_parameters": true}, "metrics": {"accuracy": 0.9773333333333334, "f1_score": 0.9772888189032446}, "training_time": 3.3532967567443848, "model_path": "saved_models/xgboost_severity_level_20250602_030638.pkl"}, {"experiment_id": "20250602_030145", "timestamp": "2025-06-02T03:06:42.960286", "model_name": "LightGBM", "target_type": "Interaction Type", "parameters": {"boosting_type": "gbdt", "num_leaves": 31, "max_depth": -1, "learning_rate": 0.1, "n_estimators": 200, "subsample_for_bin": 200000, "objective": "multiclass", "min_split_gain": 0.0, "min_child_weight": 0.001, "min_child_samples": 20, "subsample": 1.0, "subsample_freq": 0, "colsample_bytree": 1.0, "reg_alpha": 0.0, "reg_lambda": 0.0, "random_state": 42, "n_jobs": -1, "silent": true, "importance_type": "split"}, "metrics": {"accuracy": 0.19466666666666665, "f1_score": 0.15409117330633976}, "training_time": 3.7251062393188477, "model_path": "saved_models/lightgbm_interaction_type_20250602_030642.pkl"}, {"experiment_id": "20250602_030145", "timestamp": "2025-06-02T03:06:44.075022", "model_name": "LightGBM", "target_type": "Severity Level", "parameters": {"boosting_type": "gbdt", "num_leaves": 31, "max_depth": -1, "learning_rate": 0.1, "n_estimators": 200, "subsample_for_bin": 200000, "objective": "multiclass", "min_split_gain": 0.0, "min_child_weight": 0.001, "min_child_samples": 20, "subsample": 1.0, "subsample_freq": 0, "colsample_bytree": 1.0, "reg_alpha": 0.0, "reg_lambda": 0.0, "random_state": 42, "n_jobs": -1, "silent": true, "importance_type": "split"}, "metrics": {"accuracy": 0.971, "f1_score": 0.9708582325620813}, "training_time": 1.0027575492858887, "model_path": "saved_models/lightgbm_severity_level_20250602_030644.pkl"}, {"experiment_id": "20250602_030145", "timestamp": "2025-06-02T03:08:03.661252", "model_name": "CNN", "target_type": "Interaction Type", "parameters": {"total_params": 21276, "epochs_trained": 50, "final_train_loss": 1.460413932800293, "final_val_loss": 1.317496657371521, "filters_1": 64, "filters_2": 32, "filters_3": 16, "kernel_size": 3, "pool_size": 2, "dropout_conv": 0.3, "dropout_dense": 0.5, "dense_units_1": 128, "dense_units_2": 64, "learning_rate": 0.001, "batch_size": 32, "epochs": 50, "validation_split": 0.2, "early_stopping_patience": 15, "reduce_lr_patience": 10, "reduce_lr_factor": 0.5, "min_lr": 1e-07}, "metrics": {"accuracy": 0.5306666666666666, "f1_score": 0.49764082300146917}, "training_time": 77.00784969329834, "model_path": "saved_models/cnn_interaction_type_20250602_030803.h5"}, {"experiment_id": "20250602_030145", "timestamp": "2025-06-02T03:09:19.573749", "model_name": "CNN", "target_type": "Severity Level", "parameters": {"total_params": 18611, "epochs_trained": 50, "final_train_loss": 0.6109310984611511, "final_val_loss": 0.5377485156059265, "filters_1": 64, "filters_2": 32, "filters_3": 16, "kernel_size": 3, "pool_size": 2, "dropout_conv": 0.3, "dropout_dense": 0.5, "dense_units_1": 128, "dense_units_2": 64, "learning_rate": 0.001, "batch_size": 32, "epochs": 50, "validation_split": 0.2, "early_stopping_patience": 15, "reduce_lr_patience": 10, "reduce_lr_factor": 0.5, "min_lr": 1e-07}, "metrics": {"accuracy": 0.7606666666666667, "f1_score": 0.7571447996970947}, "training_time": 74.98458623886108, "model_path": "saved_models/cnn_severity_level_20250602_030919.h5"}, {"experiment_id": "20250602_030145", "timestamp": "2025-06-02T05:29:58.311925", "model_name": "LSTM", "target_type": "Interaction Type", "parameters": {"total_params": 135404, "epochs_trained": 24, "final_train_loss": 2.228452682495117, "final_val_loss": 2.197814702987671, "lstm_units_1": 128, "lstm_units_2": 64, "dropout": 0.3, "recurrent_dropout": 0.3, "dense_units_1": 128, "dense_units_2": 64, "dropout_dense": 0.5, "learning_rate": 0.001, "batch_size": 32, "epochs": 50, "validation_split": 0.2, "early_stopping_patience": 15, "reduce_lr_patience": 10, "reduce_lr_factor": 0.5, "min_lr": 1e-07}, "metrics": {"accuracy": 0.25566666666666665, "f1_score": 0.10411273338642597}, "training_time": 8409.155714988708, "model_path": "saved_models/lstm_interaction_type_20250602_052958.h5"}, {"experiment_id": "20250602_030145", "timestamp": "2025-06-02T09:17:00.741638", "model_name": "LSTM", "target_type": "Severity Level", "parameters": {"total_params": 132739, "epochs_trained": 39, "final_train_loss": 0.9423132538795471, "final_val_loss": 0.9353700280189514, "lstm_units_1": 128, "lstm_units_2": 64, "dropout": 0.3, "recurrent_dropout": 0.3, "dense_units_1": 128, "dense_units_2": 64, "dropout_dense": 0.5, "learning_rate": 0.001, "batch_size": 32, "epochs": 50, "validation_split": 0.2, "early_stopping_patience": 15, "reduce_lr_patience": 10, "reduce_lr_factor": 0.5, "min_lr": 1e-07}, "metrics": {"accuracy": 0.5766666666666667, "f1_score": 0.4218322762508809}, "training_time": 13592.02590560913, "model_path": "saved_models/lstm_severity_level_20250602_091700.h5"}, {"experiment_id": "20250602_030145", "timestamp": "2025-06-02T09:17:36.868366", "model_name": "BERT-Inspired", "target_type": "Interaction Type", "parameters": {"total_params": 616236, "epochs_trained": 22, "final_train_loss": 0.3210626244544983, "final_val_loss": 0.7862154841423035, "embedding_dim": 256, "attention_heads": 3, "attention_dim": 128, "ff_dim_1": 512, "ff_dim_2": 256, "residual_dim": 256, "final_dense_dim": 128, "dropout_embedding": 0.3, "dropout_attention": 0.3, "dropout_ff": 0.4, "dropout_final": 0.5, "learning_rate": 0.001, "batch_size": 32, "epochs": 50, "validation_split": 0.2, "early_stopping_patience": 15, "reduce_lr_patience": 10, "reduce_lr_factor": 0.5, "min_lr": 1e-07}, "metrics": {"accuracy": 0.7793333333333333, "f1_score": 0.7714426660997401}, "training_time": 35.10207939147949, "model_path": "saved_models/bert_inspired_interaction_type_20250602_091736.h5"}, {"experiment_id": "20250602_030145", "timestamp": "2025-06-02T09:18:13.743095", "model_name": "BERT-Inspired", "target_type": "Severity Level", "parameters": {"total_params": 610947, "epochs_trained": 23, "final_train_loss": 0.06706930696964264, "final_val_loss": 0.31525593996047974, "embedding_dim": 256, "attention_heads": 3, "attention_dim": 128, "ff_dim_1": 512, "ff_dim_2": 256, "residual_dim": 256, "final_dense_dim": 128, "dropout_embedding": 0.3, "dropout_attention": 0.3, "dropout_ff": 0.4, "dropout_final": 0.5, "learning_rate": 0.001, "batch_size": 32, "epochs": 50, "validation_split": 0.2, "early_stopping_patience": 15, "reduce_lr_patience": 10, "reduce_lr_factor": 0.5, "min_lr": 1e-07}, "metrics": {"accuracy": 0.909, "f1_score": 0.9093081695412569}, "training_time": 35.9361526966095, "model_path": "saved_models/bert_inspired_severity_level_20250602_091813.h5"}]