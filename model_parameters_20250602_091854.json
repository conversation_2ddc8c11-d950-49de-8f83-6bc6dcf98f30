{"svm_params": {"C": 1.0, "kernel": "rbf", "degree": 3, "gamma": "scale", "coef0": 0.0, "shrinking": true, "probability": true, "tol": 0.001, "cache_size": 200, "class_weight": null, "verbose": false, "max_iter": -1, "decision_function_shape": "ovr", "break_ties": false, "random_state": 42}, "nb_params": {"priors": null, "var_smoothing": 1e-09}, "lr_params": {"penalty": "l2", "dual": false, "tol": 0.0001, "C": 1.0, "fit_intercept": true, "intercept_scaling": 1, "class_weight": null, "random_state": 42, "solver": "lbfgs", "max_iter": 1000, "multi_class": "multinomial", "verbose": 0, "warm_start": false, "n_jobs": null, "l1_ratio": null}, "xgb_params": {"max_depth": 6, "learning_rate": 0.3, "n_estimators": 400, "verbosity": 0, "objective": "multi:softprob", "booster": "gbtree", "tree_method": "auto", "n_jobs": -1, "gamma": 0, "min_child_weight": 1, "max_delta_step": 0, "subsample": 1, "colsample_bytree": 1, "colsample_bylevel": 1, "colsample_bynode": 1, "reg_alpha": 0, "reg_lambda": 1, "scale_pos_weight": 1, "base_score": 0.5, "random_state": 42, "num_parallel_tree": 1, "importance_type": "gain", "gpu_id": -1, "validate_parameters": true}, "lgb_params": {"boosting_type": "gbdt", "num_leaves": 31, "max_depth": -1, "learning_rate": 0.1, "n_estimators": 200, "subsample_for_bin": 200000, "objective": "multiclass", "class_weight": null, "min_split_gain": 0.0, "min_child_weight": 0.001, "min_child_samples": 20, "subsample": 1.0, "subsample_freq": 0, "colsample_bytree": 1.0, "reg_alpha": 0.0, "reg_lambda": 0.0, "random_state": 42, "n_jobs": -1, "silent": true, "importance_type": "split"}, "cnn_params": {"filters_1": 64, "filters_2": 32, "filters_3": 16, "kernel_size": 3, "pool_size": 2, "dropout_conv": 0.3, "dropout_dense": 0.5, "dense_units_1": 128, "dense_units_2": 64, "learning_rate": 0.001, "batch_size": 32, "epochs": 50, "validation_split": 0.2, "early_stopping_patience": 15, "reduce_lr_patience": 10, "reduce_lr_factor": 0.5, "min_lr": 1e-07}, "lstm_params": {"lstm_units_1": 128, "lstm_units_2": 64, "dropout": 0.3, "recurrent_dropout": 0.3, "dense_units_1": 128, "dense_units_2": 64, "dropout_dense": 0.5, "learning_rate": 0.001, "batch_size": 32, "epochs": 50, "validation_split": 0.2, "early_stopping_patience": 15, "reduce_lr_patience": 10, "reduce_lr_factor": 0.5, "min_lr": 1e-07}, "bert_params": {"embedding_dim": 256, "attention_heads": 3, "attention_dim": 128, "ff_dim_1": 512, "ff_dim_2": 256, "residual_dim": 256, "final_dense_dim": 128, "dropout_embedding": 0.3, "dropout_attention": 0.3, "dropout_ff": 0.4, "dropout_final": 0.5, "learning_rate": 0.001, "batch_size": 32, "epochs": 50, "validation_split": 0.2, "early_stopping_patience": 15, "reduce_lr_patience": 10, "reduce_lr_factor": 0.5, "min_lr": 1e-07}}